import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Polygon as MplPolygon, Rectangle, Circle, Ellipse as MplEllipse, Arc
import numpy as np
import math

class CADVisualizer:
    """专门处理可视化逻辑的模块"""

    def __init__(self):
        # 显示控制器集成
        self.display_controller = None

        # 初始化配色系统引用
        self.current_color_scheme = None

        # 设置中文字体
        self.chinese_font = None
        try:
            self.chinese_font = fm.FontProperties(fname='C:/Windows/Fonts/simhei.ttf')
        except:
            try:
                self.chinese_font = fm.FontProperties(fname='C:/Windows/Fonts/msyh.ttc')
            except:
                pass

        # 初始化图形（修改为支持颜色索引图）
        plt.ioff()  # 关闭交互模式，使用Tkinter控制

        # 🎨 优化索引图布局：左侧详细视图，右侧概览图，去除顶部标题栏
        self.fig = plt.figure(figsize=(12, 6))
        self.fig.patch.set_facecolor('#f0f0f0')

        # 🔧 优化：创建简化的2列布局，去除索引图标题栏
        # 使用2行2列布局，右侧上方概览图，右侧下方索引图（无标题）
        gs = self.fig.add_gridspec(2, 2, width_ratios=[1, 1], height_ratios=[3, 1],
                                  hspace=0.08, wspace=0.2)  # 适当调整间距

        # 左侧：详细视图（跨2行，确保与右侧总高度一致）
        self.ax_detail = self.fig.add_subplot(gs[:, 0])
        self.ax_detail.set_facecolor('#f5f5f5')

        # 右上：概览图（主要显示区域）
        self.ax_overview = self.fig.add_subplot(gs[0, 1])
        self.ax_overview.set_facecolor('#f8f8f8')

        # 右下：颜色索引图（紧凑显示，无标题栏）
        self.ax_color_legend = self.fig.add_subplot(gs[1, 1])
        self.ax_color_legend.set_facecolor('#ffffff')
        self.ax_color_legend.axis('off')  # 隐藏坐标轴

        # 设置坐标轴标签
        self.ax_detail.set_xlabel('X坐标', fontproperties=self.chinese_font)
        self.ax_detail.set_ylabel('Y坐标', fontproperties=self.chinese_font)
        self.ax_overview.set_xlabel('X坐标', fontproperties=self.chinese_font)
        self.ax_overview.set_ylabel('Y坐标', fontproperties=self.chinese_font)

        self.fig.tight_layout()

        # 🔧 修复：移除所有硬编码配色方案，完全依赖外部配色系统
        # 初始化为None，等待外部配色系统通过update_color_scheme()方法设置
        self.color_scheme = None

        # 🔧 修复：移除硬编码颜色字典，所有颜色都从配色方案获取
        # 注意：type_colors和category_colors已被移除，所有颜色获取都使用配色方案

    def set_display_controller(self, display_controller):
        """设置显示控制器"""
        self.display_controller = display_controller


    def visualize_entity_group(self, entity_group, category_mapping=None, labeled_entities=None,
                              current_group_entities=None, all_groups=None, groups_info=None, processor=None):
        """可视化实体组（修复版 - 使用正确的颜色逻辑）"""

        self.ax_detail.clear()

        # 检查实体组是否为空
        if not entity_group:
            print(f"  - ⚠️ 实体组为空，显示空状态")
            self.ax_detail.text(0.5, 0.5, '没有实体数据',
                               ha='center', va='center', transform=self.ax_detail.transAxes,
                               fontproperties=self.chinese_font, fontsize=14)
            self.ax_detail.set_title('详细视图 (空)', fontsize=12, fontproperties=self.chinese_font)
            return

        # 生成统计信息
        layer_stats = {}
        type_stats = {}
        for entity in entity_group:
            # 确保实体是字典类型
            if isinstance(entity, dict):
                layer = entity.get('layer', 'unknown')
                layer_stats[layer] = layer_stats.get(layer, 0) + 1
                entity_type = entity.get('type', 'unknown')
                type_stats[entity_type] = type_stats.get(entity_type, 0) + 1

        # 🔧 问题2修复：视图名称不随内容改变，保持简洁
        # 只显示固定的视图名称，不显示动态内容信息
        title = "详细视图"

        # 注释掉动态内容信息，保持标题简洁
        # if layer_stats:
        #     layer_info = f"图层: {', '.join([f'{k}({v})' for k, v in list(layer_stats.items())[:3]])}"
        #     title += f"\n{layer_info}"
        #
        # # 添加推断类别信息（修复字符串实体问题）
        # inferred_categories = [entity.get('inferred_category') for entity in entity_group
        #                      if isinstance(entity, dict) and entity.get('inferred_category')]
        # if inferred_categories and category_mapping:
        #     most_common_category = max(set(inferred_categories), key=inferred_categories.count)
        #     category_text = category_mapping.get(most_common_category, most_common_category)
        #     title += f"\n推断类别: {category_text}"

        self.ax_detail.set_title(title, fontsize=12, fontproperties=self.chinese_font)
        self.ax_detail.set_aspect('equal')
        self.ax_detail.grid(True, linestyle='--', alpha=0.7)

        # 🔧 修复：移除硬编码颜色调色板，统一使用配色方案
        for i, entity in enumerate(entity_group):
            # 确保实体是字典类型（修复字符串实体问题）
            if not isinstance(entity, dict):
                print(f"    ⚠️ 跳过非字典实体: {type(entity)} - {entity}")
                continue

            try:
                # 使用基于组信息的颜色获取
                group_info = self._find_entity_group_info_simple(entity, all_groups, groups_info)
                color = self._get_group_color(group_info)
                alpha = 0.8
                linewidth = 1.0

                self._draw_entity(entity, color, linewidth, alpha, self.ax_detail)

            except Exception as e:
                print(f"    ❌ 实体{i+1}绘制失败: {e}")

            # 在实体旁显示图层名和类别信息
            centroid = self._get_entity_centroid(entity)
            if centroid is not None:
                # 显示图层名
                # 🔧 修复：使用配色系统获取背景颜色
                bg_color = self.color_scheme.get('background', '#FFFFFF')
                self.ax_detail.text(centroid[0], centroid[1], layer,
                            fontsize=6, color=color, alpha=0.7,
                            bbox=dict(facecolor=bg_color, alpha=0.5, edgecolor='none', pad=1))

                # 如果实体有标签，显示类别信息
                if 'label' in entity and category_mapping:
                    label_text = category_mapping.get(entity['label'], entity['label'])
                    # 🔧 修复：使用配色系统获取错误提示颜色
                    error_color = self.color_scheme.get('highlight', '#FF0000')
                    error_bg = self.color_scheme.get('labeling', '#FFFF00')
                    self.ax_detail.text(centroid[0], centroid[1] + 20, f"类别: {label_text}",
                                fontsize=8, color=error_color, alpha=0.8,
                                bbox=dict(facecolor=error_bg, alpha=0.7, edgecolor=error_color, pad=2),
                                fontproperties=self.chinese_font)

        # 🔑 关键修复：处理大坐标值的显示问题
        try:
            # 获取当前坐标范围
            self.ax_detail.autoscale_view()

            # 检查坐标范围是否过大
            xlim = self.ax_detail.get_xlim()
            ylim = self.ax_detail.get_ylim()

            x_range = xlim[1] - xlim[0]
            y_range = ylim[1] - ylim[0]

            print(f"  - 坐标范围: X={xlim[0]:.1f}~{xlim[1]:.1f} (范围:{x_range:.1f}), Y={ylim[0]:.1f}~{ylim[1]:.1f} (范围:{y_range:.1f})")

            # 如果坐标范围很小（可能是重叠的点），扩展显示范围
            if x_range < 1.0:
                center_x = (xlim[0] + xlim[1]) / 2
                self.ax_detail.set_xlim(center_x - 50, center_x + 50)
                print(f"  - 扩展X轴显示范围")

            if y_range < 1.0:
                center_y = (ylim[0] + ylim[1]) / 2
                self.ax_detail.set_ylim(center_y - 50, center_y + 50)
                print(f"  - 扩展Y轴显示范围")

            # 强制刷新坐标轴
            self.ax_detail.relim()
            self.ax_detail.autoscale_view(tight=False)

        except Exception as e:
            print(f"  - ⚠️ 坐标范围调整失败: {e}")

        print(f"  - ✅ 实体组绘制完成，共绘制 {len(entity_group)} 个实体")

    def visualize_overview(self, all_entities, current_group_entities=None, labeled_entities=None,
                          processor=None, current_group_index=None, wall_fills=None, wall_fill_processor=None,
                          hidden_groups=None, all_groups=None, groups_info=None):
        """概览图显示 - 完全基于组信息，与替代视图一致"""

        # 数据来源：基于组列表+组信息
        if all_groups is None:
            all_groups = []
        if groups_info is None:
            groups_info = []

        # 从处理器获取组数据
        if processor and (not all_groups or not groups_info):
            if not all_groups:
                all_groups = getattr(processor, 'all_groups', [])
            if not groups_info:
                groups_info = getattr(processor, 'groups_info', [])

        # 使用与替代视图完全一致的绘制方法
        self._draw_overview_by_groups(all_groups, groups_info, current_group_index, hidden_groups)

    def _draw_overview_by_groups(self, all_groups, groups_info, current_group_index=None, hidden_groups=None):
        """基于组遍历绘制概览图 - 与替代视图完全一致"""
        try:
            # 清除概览图
            self.ax_overview.clear()
            self.ax_overview.set_title('全图概览', fontsize=12, fontweight='bold')
            self.ax_overview.set_aspect('equal')
            self.ax_overview.grid(True, linestyle='--', alpha=0.3)

            # 颜色索引：记录使用的颜色
            self.used_colors = {}

            # 遍历方式：基于组遍历
            for group_index, group in enumerate(all_groups):
                try:
                    # 获取组信息
                    group_info = None
                    if groups_info and group_index < len(groups_info):
                        group_info = groups_info[group_index]

                    # 标记当前组
                    if current_group_index is not None and group_index == current_group_index:
                        if group_info is None:
                            group_info = {}
                        group_info['is_current_group'] = True

                    # 颜色获取：完全基于组信息，不使用实体属性
                    color = self._get_group_color(group_info)

                    # 记录颜色使用
                    self._record_group_color_usage(color, group_info)

                    # 绘制组中的所有实体
                    entities = group if isinstance(group, list) else group.get('entities', [])
                    for entity in entities:
                        try:
                            # 线条样式：细线条
                            self._draw_entity_thin(entity, color, self.ax_overview)
                        except Exception as e:
                            print(f"绘制实体失败: {e}")

                except Exception as e:
                    print(f"绘制组 {group_index+1} 失败: {e}")

            # 调整视图范围
            self.ax_overview.relim()
            self.ax_overview.autoscale_view()

            # 刷新画布
            self.fig.canvas.draw()

        except Exception as e:
            print(f"概览图绘制失败: {e}")

    def _get_group_color(self, group_info):
        """基于组信息获取颜色 - 完全不使用实体属性"""
        try:
            if not group_info:
                return self._get_color_from_scheme('other')

            # 优先级1：当前组
            if group_info.get('is_current_group'):
                return self._get_color_from_scheme('current_group')

            # 优先级2：根据状态和标签获取颜色
            status = group_info.get('status', '').lower().strip()
            label = group_info.get('label', '').lower().strip()

            # 已标注的组根据标签获取颜色
            if status in ['labeled', 'auto_labeled'] and label and label != '未标注':
                color_key = self._map_label_to_color_key(label)
                return self._get_color_from_scheme(color_key)

            # 未标注的组
            if status == 'unlabeled':
                return self._get_color_from_scheme('unlabeled')

            # 默认
            return self._get_color_from_scheme('other')

        except Exception as e:
            return self._get_color_from_scheme('other')

    def _map_label_to_color_key(self, label):
        """标签映射到配色键"""
        label_mapping = {
            'wall': 'wall', '墙体': 'wall', '墙': 'wall',
            'door_window': 'door_window', '门窗': 'door_window', '门': 'door_window', '窗': 'door_window',
            'furniture': 'furniture', '家具': 'furniture',
            'bed': 'bed', '床': 'bed',
            'sofa': 'sofa', '沙发': 'sofa',
            'cabinet': 'cabinet', '柜子': 'cabinet', '柜': 'cabinet',
            'dining_table': 'dining_table', '餐桌': 'dining_table', '桌子': 'dining_table',
            'appliance': 'appliance', '电器': 'appliance',
            'stair': 'stair', '楼梯': 'stair',
            'elevator': 'elevator', '电梯': 'elevator',
            'column': 'column', '柱子': 'column', '柱': 'column',
            'railing': 'railing', '栏杆': 'railing',
            'dimension': 'dimension', '尺寸': 'dimension',
            'text': 'text', '文字': 'text'
        }
        return label_mapping.get(label, 'other')

    def _get_color_from_scheme(self, color_key):
        """从配色方案获取颜色"""
        try:
            if hasattr(self, 'current_color_scheme') and self.current_color_scheme:
                if color_key in self.current_color_scheme:
                    return self.current_color_scheme[color_key]

            # 默认颜色
            default_colors = {
                'current_group': '#FF0000',  # 红色
                'wall': '#0000FF',           # 蓝色
                'door_window': '#00FF00',    # 绿色
                'furniture': '#FFFF00',      # 黄色
                'bed': '#FF8000',            # 橙色
                'sofa': '#8000FF',           # 紫色
                'cabinet': '#FF0080',        # 粉色
                'dining_table': '#00FFFF',   # 青色
                'appliance': '#80FF00',      # 黄绿色
                'stair': '#FF8080',          # 浅红色
                'elevator': '#8080FF',       # 浅蓝色
                'column': '#80FF80',         # 浅绿色
                'railing': '#FFFF80',        # 浅黄色
                'dimension': '#FF80FF',      # 浅紫色
                'text': '#80FFFF',           # 浅青色
                'unlabeled': '#A0A0A0',      # 灰色
                'other': '#808080'           # 深灰色
            }
            return default_colors.get(color_key, '#808080')

        except Exception as e:
            return '#808080'

    def _record_group_color_usage(self, color, group_info):
        """记录组颜色使用情况"""
        try:
            if group_info:
                label = group_info.get('label', '其他')
                if label not in self.used_colors:
                    self.used_colors[label] = {
                        'color': color,
                        'count': 0
                    }
                self.used_colors[label]['count'] += 1
            else:
                if '其他' not in self.used_colors:
                    self.used_colors['其他'] = {
                        'color': color,
                        'count': 0
                    }
                self.used_colors['其他']['count'] += 1

        except Exception as e:
            pass

    def _draw_entity_thin(self, entity, color, axis):
        """绘制实体 - 细线条样式"""
        try:
            entity_type = entity.get('type', 'LINE')

            if entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                self._draw_line_thin(entity, color, axis)
            elif entity_type == 'CIRCLE':
                self._draw_circle_thin(entity, color, axis)
            elif entity_type == 'ARC':
                self._draw_arc_thin(entity, color, axis)
            elif entity_type == 'INSERT':
                self._draw_insert_thin(entity, color, axis)
            elif entity_type in ['TEXT', 'MTEXT']:
                self._draw_text_thin(entity, color, axis)

        except Exception as e:
            pass

    def _draw_line_thin(self, entity, color, axis):
        """绘制线条 - 细线条"""
        if 'start' in entity and 'end' in entity:
            start = entity['start']
            end = entity['end']
            axis.plot([start[0], end[0]], [start[1], end[1]], color=color, linewidth=0.8)
        elif 'points' in entity:
            points = entity['points']
            if len(points) >= 2:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                axis.plot(x_coords, y_coords, color=color, linewidth=0.8)

    def _draw_circle_thin(self, entity, color, axis):
        """绘制圆形 - 细线条"""
        if 'center' in entity and 'radius' in entity:
            center = entity['center']
            radius = entity['radius']
            from matplotlib import patches
            circle = patches.Circle(center, radius, fill=False, edgecolor=color, linewidth=0.8)
            axis.add_patch(circle)

    def _draw_arc_thin(self, entity, color, axis):
        """绘制弧形 - 细线条"""
        if 'center' in entity and 'radius' in entity:
            center = entity['center']
            radius = entity['radius']
            start_angle = entity.get('start_angle', 0)
            end_angle = entity.get('end_angle', 360)
            from matplotlib import patches
            arc = patches.Arc(center, 2*radius, 2*radius,
                            theta1=start_angle, theta2=end_angle,
                            color=color, linewidth=0.8)
            axis.add_patch(arc)

    def _draw_insert_thin(self, entity, color, axis):
        """绘制插入实体"""
        if 'insertion_point' in entity:
            point = entity['insertion_point']
            axis.plot(point[0], point[1], 'o', color=color, markersize=6)

    def _draw_text_thin(self, entity, color, axis):
        """绘制文字实体"""
        if 'insertion_point' in entity:
            point = entity['insertion_point']
            text = entity.get('text', 'T')
            axis.text(point[0], point[1], text, color=color, fontsize=6)

    def _find_entity_group_info_simple(self, entity, all_groups, groups_info):
        """简化的查找实体组信息方法"""
        try:
            if not all_groups:
                return None

            entity_id = entity.get('id')
            if entity_id is None:
                return None

            for group_index, group in enumerate(all_groups):
                entities = group if isinstance(group, list) else group.get('entities', [])

                for group_entity in entities:
                    if isinstance(group_entity, dict) and group_entity.get('id') == entity_id:
                        if groups_info and group_index < len(groups_info):
                            return groups_info[group_index]
                        return None

            return None

        except Exception as e:
            return None

























    def _draw_entity_alternative_style(self, entity, color, axis):
        """🔧 新增：使用替代视图风格绘制实体"""
        try:
            # 使用替代视图的细线条风格
            if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                self._draw_line_entity_alternative(entity, color, axis)
            elif entity.get('type') == 'CIRCLE':
                self._draw_circle_entity_alternative(entity, color, axis)
            elif entity.get('type') == 'ARC':
                self._draw_arc_entity_alternative(entity, color, axis)
            else:
                # 默认按线条处理
                self._draw_line_entity_alternative(entity, color, axis)
        except Exception as e:
            print(f"⚠️ 替代风格绘制实体失败: {e}")

    def _draw_line_entity_alternative(self, entity, color, axis):
        """🔧 新增：绘制线条实体（替代视图风格 - 细线条）"""
        if 'start' in entity and 'end' in entity:
            start = entity['start']
            end = entity['end']
            # 替代视图风格：线条宽度0.8，更细
            axis.plot([start[0], end[0]], [start[1], end[1]], color=color, linewidth=0.8)
        elif 'points' in entity:
            points = entity['points']
            if len(points) >= 2:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                # 替代视图风格：线条宽度0.8，更细
                axis.plot(x_coords, y_coords, color=color, linewidth=0.8)

    def _draw_circle_entity_alternative(self, entity, color, axis):
        """🔧 新增：绘制圆形实体（替代视图风格）"""
        if 'center' in entity and 'radius' in entity:
            import matplotlib.patches as patches
            circle = patches.Circle(entity['center'], entity['radius'],
                                  fill=False, edgecolor=color, linewidth=0.8)
            axis.add_patch(circle)

    def _draw_arc_entity_alternative(self, entity, color, axis):
        """🔧 新增：绘制弧形实体（替代视图风格）"""
        # 简化处理：按线条绘制
        self._draw_line_entity_alternative(entity, color, axis)

    def _adjust_view_limits_alternative(self):
        """🔧 新增：调整视图范围（替代视图风格）"""
        try:
            # 替代视图风格：只调整概览图的范围
            self.ax_overview.relim()
            self.ax_overview.autoscale_view()
        except:
            # 如果自动调整失败，设置默认范围
            self.ax_overview.set_xlim(-100, 100)
            self.ax_overview.set_ylim(-100, 100)

    def _update_group_status_stats(self, group_info, group_status_stats):
        """🔧 新增：更新组状态统计（用于智能双列布局图例）"""
        try:
            if not group_info:
                group_status_stats['unlabeled'] += 1
                return

            # 检查是否为当前组
            if group_info.get('is_current_group'):
                group_status_stats['current'] += 1
                return

            # 检查组状态
            status = group_info.get('status', '').lower().strip()
            if status in ['labeled', 'relabeled']:
                group_status_stats['labeled'] += 1
            elif status == 'auto_labeled':
                group_status_stats['auto_labeled'] += 1
            elif status in ['labeling', 'pending']:
                group_status_stats['current'] += 1
            else:
                group_status_stats['unlabeled'] += 1

        except Exception as e:
            print(f"⚠️ 更新组状态统计失败: {e}")
            group_status_stats['unlabeled'] += 1

    def _record_color_usage_alternative(self, color, group_info, used_colors):
        """🔧 新增：记录颜色使用情况（与替代视图一致的逻辑）"""
        try:
            # 获取颜色键
            color_key = self._map_group_info_to_color_key(group_info) if group_info else 'unlabeled'

            # 获取颜色标签
            color_label = self._get_color_label(color_key)

            # 记录使用情况
            if color_key not in used_colors:
                used_colors[color_key] = {
                    'color': color,
                    'label': color_label,
                    'count': 0
                }

            used_colors[color_key]['count'] += 1

        except Exception as e:
            print(f"⚠️ 记录颜色使用失败: {e}")

    def _draw_wall_fills_if_available(self, wall_fills, wall_fill_processor):
        """🔧 新增：绘制墙体填充（如果可用）"""
        try:
            # 检查是否有全局的墙体填充数据（用于V2版本）
            global_wall_fills = getattr(self, '_global_wall_fills', None)
            global_wall_fill_processor = getattr(self, '_global_wall_fill_processor', None)

            # 优先使用传入的参数，如果没有则使用全局数据
            effective_wall_fills = wall_fills if wall_fills is not None else global_wall_fills
            effective_wall_fill_processor = wall_fill_processor if wall_fill_processor is not None else global_wall_fill_processor

            if effective_wall_fills and effective_wall_fill_processor:
                patches_list = effective_wall_fill_processor.create_fill_patches(effective_wall_fills)
                for patch in patches_list:
                    self.ax_overview.add_patch(patch)
                print(f"  ✅ 绘制了 {len(patches_list)} 个墙体填充")

        except Exception as e:
            print(f"⚠️ 绘制墙体填充失败: {e}")

    def _create_enhanced_legend_with_alternative_data(self, used_colors, group_status_stats):
        """🔧 新增：创建智能双列布局的增强图例（使用替代视图数据）"""
        try:
            import matplotlib.patches as patches

            # 收集图例项目
            legend_patches = []

            # 第一部分：组状态图例（与替代视图一致的数据）
            if any(count > 0 for count in group_status_stats.values()):
                # 当前标注组
                if group_status_stats.get('current', 0) > 0:
                    current_color = self.color_scheme.get('current_group', '#FF0000')
                    patch = patches.Patch(color=current_color,
                                        label=f'正在标注 ({group_status_stats["current"]}个组)')
                    legend_patches.append(patch)

                # 已手动标注组
                if group_status_stats.get('labeled', 0) > 0:
                    labeled_color = self.color_scheme.get('labeled', '#00FF00')
                    patch = patches.Patch(color=labeled_color,
                                        label=f'已标注 ({group_status_stats["labeled"]}个组)')
                    legend_patches.append(patch)

                # 自动标注组
                if group_status_stats.get('auto_labeled', 0) > 0:
                    auto_color = self.color_scheme.get('auto_labeled', '#0080FF')
                    patch = patches.Patch(color=auto_color,
                                        label=f'自动标注 ({group_status_stats["auto_labeled"]}个组)')
                    legend_patches.append(patch)

                # 未标注组
                if group_status_stats.get('unlabeled', 0) > 0:
                    unlabeled_color = self.color_scheme.get('unlabeled', '#808080')
                    patch = patches.Patch(color=unlabeled_color,
                                        label=f'未标注 ({group_status_stats["unlabeled"]}个组)')
                    legend_patches.append(patch)

            # 第二部分：实体类别图例（从替代视图数据中提取）
            category_patches = []
            for color_key, info in used_colors.items():
                if color_key not in ['current_group', 'pending', 'unlabeled'] and info['count'] > 0:
                    patch = patches.Patch(color=info['color'],
                                        label=f"{info['label']} ({info['count']}个组)")
                    category_patches.append(patch)

            # 合并所有图例项目
            legend_patches.extend(category_patches)

            # 显示智能双列布局图例
            if legend_patches:
                # 智能双列布局：超过8个项目时使用双列
                ncol = 2 if len(legend_patches) > 8 else 1
                legend = self.ax_overview.legend(
                    handles=legend_patches,
                    loc='upper right',
                    fontsize=8,
                    prop=self.chinese_font,
                    ncol=ncol,  # 保留智能双列布局
                    framealpha=0.9,
                    fancybox=True,
                    shadow=True
                )

                # 设置图例边框（使用配色系统）
                legend_bg = self.color_scheme.get('background', '#FFFFFF')
                legend_edge = self.color_scheme.get('text_secondary', '#808080')
                legend.get_frame().set_facecolor(legend_bg)
                legend.get_frame().set_edgecolor(legend_edge)
                legend.get_frame().set_linewidth(1)

                print(f"  - ✅ 智能双列布局增强图例创建完成: {len(legend_patches)} 个项目")

        except Exception as e:
            print(f"  - ❌ 创建智能双列布局增强图例失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_alternative_color_index(self, used_colors):
        """🔧 保留：创建替代视图风格的颜色索引（用于简化场景）"""
        try:
            # 简化的颜色索引，不绘制复杂的图例
            print(f"  🎨 颜色索引: {len(used_colors)}种颜色")
            for color_key, info in used_colors.items():
                print(f"    - {info['label']}: {info['color']} ({info['count']}个组)")
        except Exception as e:
            print(f"⚠️ 创建颜色索引失败: {e}")

    def _draw_wall_fills_if_available(self, wall_fills, wall_fill_processor):
        """🔧 新增：绘制墙体填充（如果有）"""
        try:
            if wall_fills and wall_fill_processor:
                patches_list = wall_fill_processor.create_fill_patches(wall_fills)
                for patch in patches_list:
                    self.ax_overview.add_patch(patch)
                print(f"  ✅ 绘制了{len(patches_list)}个墙体填充")
        except Exception as e:
            print(f"⚠️ 绘制墙体填充失败: {e}")



    def _find_entity_group_index(self, entity, all_groups):
        """查找实体所属的组索引（修复unhashable dict问题）"""
        if not all_groups:
            return None

        for group_index, group in enumerate(all_groups):
            # 处理不同格式的组（字典格式或列表格式）
            if isinstance(group, dict):
                entities = group.get('entities', [])
            elif isinstance(group, list):
                entities = group
            else:
                continue

            # 使用安全的实体比较方法
            if self._is_entity_in_group_safe(entity, entities):
                return group_index

        return None

    def _is_entity_in_group_safe(self, entity, entities):
        """安全地检查实体是否在组中（避免unhashable dict错误）"""
        # 处理None值和空列表
        if not entities:
            return False

        try:
            # 方法1: 尝试直接比较（对于可哈希的对象）
            if entity in entities:
                return True
        except (TypeError, AttributeError):
            # 如果出现unhashable错误或其他类型错误，使用ID比较
            pass

        # 方法2: 使用ID比较
        entity_id = id(entity)
        for group_entity in entities:
            if id(group_entity) == entity_id:
                return True

        # 方法3: 如果都是字典，使用内容比较（最后手段）
        if isinstance(entity, dict):
            for group_entity in entities:
                if isinstance(group_entity, dict) and self._dict_entities_equal(entity, group_entity):
                    return True

        return False

    def _dict_entities_equal(self, entity1, entity2):
        """比较两个字典实体是否相等（基于关键属性）"""
        try:
            # 🔧 修复：优先使用ID比较
            if entity1.get('id') is not None and entity2.get('id') is not None:
                return entity1.get('id') == entity2.get('id')

            # 🔧 修复：扩展关键属性列表，包含更多可能的属性
            key_attrs = ['type', 'layer', 'points', 'center', 'radius', 'start', 'end', 'length']

            # 至少要有一些基本属性匹配
            basic_match = (entity1.get('type') == entity2.get('type') and
                          entity1.get('layer') == entity2.get('layer'))

            if not basic_match:
                return False

            # 检查几何属性
            for attr in key_attrs:
                val1 = entity1.get(attr)
                val2 = entity2.get(attr)

                # 如果两个实体都有这个属性，必须相等
                if val1 is not None and val2 is not None:
                    if val1 != val2:
                        return False

            return True
        except Exception:
            return False

    def highlight_selected_entities(self, group_entities, selected_entities):
        """高亮选中实体（修复版 - 使用配色方案）"""
        # 重新绘制实体组，高亮选中的实体
        self.ax_detail.clear()

        for entity in group_entities:
            if self._is_entity_in_group_safe(entity, selected_entities):
                # 🔧 修复：高亮选中的实体 - 使用配色方案中的高亮颜色
                color = self.color_scheme.get('highlight', self.color_scheme.get('current_group', '#FF0000'))
                entity_type = entity.get('type', 'unknown')
                if entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    linewidth = 4.0  # 线条类实体使用更粗的线宽
                else:
                    linewidth = 3.0
                alpha = 1.0
            else:
                # 🔧 修复：普通实体 - 使用配色方案颜色而不是type_colors
                entity_type = entity.get('type', 'unknown')
                # 根据实体类型获取配色方案中的对应颜色
                if entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    color = self.color_scheme.get('wall', self.color_scheme.get('other', '#808080'))
                    linewidth = 2.0  # 线条类实体基础线宽
                elif entity_type == 'INSERT':
                    color = self.color_scheme.get('furniture', self.color_scheme.get('other', '#808080'))
                    linewidth = 1.5
                elif entity_type in ['TEXT', 'MTEXT']:
                    color = self.color_scheme.get('text', self.color_scheme.get('other', '#808080'))
                    linewidth = 1.5
                elif entity_type == 'DIMENSION':
                    color = self.color_scheme.get('dimension', self.color_scheme.get('other', '#808080'))
                    linewidth = 1.5
                else:
                    color = self.color_scheme.get('other', '#808080')
                    linewidth = 1.5
                alpha = 0.6  # 非选中实体透明度降低，突出选中效果

            self._draw_entity(entity, color, linewidth, alpha, self.ax_detail)

        self.ax_detail.set_title(f"选中 {len(selected_entities)} 个实体",
                                fontsize=12, fontproperties=self.chinese_font)
        self.ax_detail.set_aspect('equal')
        self.ax_detail.grid(True, linestyle='--', alpha=0.7)
        self.ax_detail.autoscale_view()

    def _draw_entity(self, entity, color, linewidth, alpha, ax):
        """绘制单个实体"""
        try:
            # 安全获取实体类型
            entity_type = entity.get('type', 'unknown')

            if entity_type == 'LINE':
                # 🔑 修复：支持两种数据格式
                if 'points' in entity:
                    # 格式1：points数组
                    points = entity['points']
                    if len(points) >= 2:
                        x = [p[0] for p in points[:2]]
                        y = [p[1] for p in points[:2]]
                elif 'start_x' in entity and 'start_y' in entity and 'end_x' in entity and 'end_y' in entity:
                    # 格式2：start_x, start_y, end_x, end_y字段
                    x = [entity['start_x'], entity['end_x']]
                    y = [entity['start_y'], entity['end_y']]
                elif 'start' in entity and 'end' in entity:
                    # 格式3：start, end数组
                    start = entity['start']
                    end = entity['end']
                    x = [start[0], end[0]]
                    y = [start[1], end[1]]
                else:
                    print(f"  ⚠️ LINE实体缺少坐标信息: {list(entity.keys())}")
                    return

                # 🔑 关键修复：检查坐标有效性
                if all(isinstance(coord, (int, float)) and not (coord != coord) for coord in x + y):  # 检查NaN
                    ax.plot(x, y, color=color, linewidth=linewidth, alpha=alpha)
                else:
                    print(f"  ⚠️ 跳过无效LINE坐标: x={x}, y={y}")
        except Exception as e:
            print(f"  ⚠️ LINE绘制失败: {e}")

        entity_type = entity.get('type', 'unknown')
        if entity_type in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity:
            try:
                points = entity['points']
                if points:
                    x = [p[0] for p in points]
                    y = [p[1] for p in points]

                    # 🔑 关键修复：检查坐标有效性
                    if all(isinstance(coord, (int, float)) and not (coord != coord) for coord in x + y):
                        if entity.get('closed', False) and len(points) >= 3:
                            poly = MplPolygon(points, closed=True, fill=False,
                                             edgecolor=color, linewidth=linewidth, alpha=alpha)
                            ax.add_patch(poly)
                        else:
                            ax.plot(x, y, color=color, linewidth=linewidth, marker='o', markersize=3, alpha=alpha)
                    else:
                        print(f"  ⚠️ 跳过无效POLYLINE坐标: {len(points)}个点")
            except Exception as e:
                print(f"  ⚠️ POLYLINE绘制失败: {e}")

        if entity_type == 'CIRCLE' and 'center' in entity and 'radius' in entity:
            try:
                center = entity['center']
                radius = entity['radius']

                # 🔑 关键修复：检查坐标和半径有效性
                if (isinstance(center, (list, tuple)) and len(center) >= 2 and
                    all(isinstance(coord, (int, float)) and not (coord != coord) for coord in center[:2]) and
                    isinstance(radius, (int, float)) and radius > 0 and not (radius != radius)):

                    circle = Circle(center[:2], radius,
                                   fill=False, edgecolor=color, linewidth=linewidth, alpha=alpha)
                    ax.add_patch(circle)
                else:
                    print(f"  ⚠️ 跳过无效CIRCLE: center={center}, radius={radius}")
            except Exception as e:
                print(f"  ⚠️ CIRCLE绘制失败: {e}")

        if entity_type == 'ARC' and 'center' in entity and 'radius' in entity:
            # 正确绘制圆弧（改进版：精确镜像处理）
            center = entity['center']
            radius = entity['radius']
            # 🔑 关键修复：安全获取角度属性，提供默认值
            start_angle = entity.get('start_angle', 0)
            end_angle = entity.get('end_angle', 360)

            # 角度单位转换：如果角度值很小，可能是弧度制，需要转换为角度制
            if abs(start_angle) <= 2*np.pi and abs(end_angle) <= 2*np.pi:
                start_angle = np.degrees(start_angle)
                end_angle = np.degrees(end_angle)

            # 检查是否是完整圆（360度）
            angle_diff = abs(end_angle - start_angle)
            if abs(angle_diff - 360) < 1e-6 or abs(angle_diff - 2*np.pi) < 1e-6:
                # 完整圆，使用0到360度
                start_angle = 0
                end_angle = 360
            else:
                # 检查圆弧方向和镜像状态
                is_mirrored = self._check_arc_mirrored(entity)

                # 半径计算修正（处理非均匀缩放）
                scale_x = entity.get('scale_x', 1.0)
                scale_y = entity.get('scale_y', 1.0)
                if scale_x != 1.0 or scale_y != 1.0:
                    # 使用几何平均处理非均匀缩放
                    import math
                    radius = abs(radius * math.sqrt(abs(scale_x * scale_y)))

                # 标准化角度到0-360度范围
                start_angle = start_angle % 360
                end_angle = end_angle % 360

                # 处理镜像圆弧的角度
                if is_mirrored:
                    print(f"⚠️ 检测到镜像变换！原始角度: 起点={start_angle:.1f}°, 终点={end_angle:.1f}°")
                    start_angle, end_angle = self._fix_mirrored_arc_angles(start_angle, end_angle)
                    print(f"变换后角度: 起点={start_angle:.1f}°, 终点={end_angle:.1f}°")

                # 处理跨越0度的圆弧
                if end_angle < start_angle:
                    # 如果结束角度小于开始角度，说明圆弧跨越了0度
                    if (start_angle - end_angle) > 180:
                        # 圆弧跨越0度，调整结束角度
                        end_angle += 360
                    else:
                        # 可能是逆时针圆弧，交换角度
                        start_angle, end_angle = end_angle, start_angle

            # 使用matplotlib的Arc patch绘制圆弧（增强错误处理）
            try:
                # 确保参数有效
                if radius <= 0:
                    print(f"⚠️ 圆弧半径无效: {radius}")
                    return

                # 确保角度差异合理
                angle_span = abs(end_angle - start_angle)
                if angle_span > 360:
                    end_angle = start_angle + 360

                arc = Arc(center, 2*radius, 2*radius,
                         theta1=start_angle, theta2=end_angle,
                         edgecolor=color, linewidth=linewidth, alpha=alpha,
                         fill=False)
                ax.add_patch(arc)
            except Exception as e:
                print(f"⚠️ 圆弧绘制失败: {e}")
                print(f"   参数: 中心={center}, 半径={radius}, 角度={start_angle:.1f}°-{end_angle:.1f}°")
                # 降级处理：绘制完整圆形
                try:
                    circle = Circle(center, radius, edgecolor=color, fill=False,
                                  linewidth=linewidth, alpha=alpha)
                    ax.add_patch(circle)
                    print(f"   已降级为完整圆形")
                except Exception as e2:
                    print(f"   圆形降级也失败: {e2}")

        if entity_type == 'INSERT' and 'position' in entity:
            position = entity['position']
            rect = Rectangle((position[0]-100, position[1]-100), 200, 200,
                            edgecolor=color, fill=False, linewidth=linewidth, alpha=alpha)
            ax.add_patch(rect)
            # 添加块名称
            # 🔧 修复：使用配色系统获取文本背景颜色
            text_bg = self.color_scheme.get('background', '#FFFFFF')
            ax.text(position[0], position[1], entity.get('block', 'BLK'),
                    fontsize=8, ha='center', va='center', color=color,
                    bbox=dict(facecolor=text_bg, alpha=0.7, edgecolor='none'))

        if entity_type in ['TEXT', 'MTEXT'] and 'position' in entity:
            position = entity['position']
            text = entity.get('text', '')
            height = entity.get('height', 3) * 10  # 缩放文本大小
            # 🔧 修复：使用配色系统获取文本背景颜色
            text_bg = self.color_scheme.get('background', '#FFFFFF')
            ax.text(position[0], position[1], text,
                    fontsize=min(height, 20),  # 限制最大字体大小
                    color=color, ha='left', va='bottom',
                    bbox=dict(facecolor=text_bg, alpha=0.7, edgecolor='none'))

        if entity_type == 'DIMENSION' and 'def_points' in entity:
            # 绘制尺寸线
            points = [p for p in entity['def_points'] if p is not None]
            if len(points) >= 2:
                x = [p[0] for p in points]
                y = [p[1] for p in points]
                ax.plot(x, y, 'g--', linewidth=1, alpha=alpha)

            # 绘制尺寸文本
            if points:
                text_pos = points[1] if len(points) > 1 else points[0]
                # 🔧 修复：使用配色系统获取文字和背景颜色
                text_color = self.color_scheme.get('text_success', '#008000')
                bg_color = self.color_scheme.get('background', '#FFFFFF')
                ax.text(text_pos[0], text_pos[1], entity.get('text', 'DIM'),
                        fontsize=8, ha='center', va='center', color=text_color,
                        bbox=dict(facecolor=bg_color, alpha=0.7, edgecolor='none'))
        elif entity_type == 'ELLIPSE' and 'center' in entity and 'major_axis' in entity and 'ratio' in entity:
            # 椭圆绘制（改进版：支持镜像处理和数据类型检查）
            try:
                center = entity['center']
                major_axis = entity['major_axis']
                ratio = entity['ratio']
                start_param = entity.get('start_param', 0)
                end_param = entity.get('end_param', 2 * np.pi)

                # 确保数据类型正确
                if isinstance(center, (list, tuple)) and len(center) >= 2:
                    center = [float(center[0]), float(center[1])]
                else:
                    print(f"⚠️ 椭圆center数据无效: {center}")
                    return

                if isinstance(major_axis, (list, tuple)) and len(major_axis) >= 2:
                    major_axis = [float(major_axis[0]), float(major_axis[1])]
                else:
                    print(f"⚠️ 椭圆major_axis数据无效: {major_axis}")
                    return

                ratio = float(ratio)
                start_param = float(start_param)
                end_param = float(end_param)

                # 检查椭圆是否被镜像
                is_mirrored = self._check_arc_mirrored(entity)

                # 计算椭圆参数
                major_axis_array = np.array(major_axis, dtype=float)
                a = float(np.linalg.norm(major_axis_array))
                b = float(a * ratio)
                angle = float(np.arctan2(major_axis_array[1], major_axis_array[0]))
            except (ValueError, TypeError) as e:
                print(f"⚠️ 椭圆数据类型转换失败: {e}")
                print(f"   center: {entity.get('center')}")
                print(f"   major_axis: {entity.get('major_axis')}")
                print(f"   ratio: {entity.get('ratio')}")
                return

            # 处理镜像变换对椭圆的影响
            if is_mirrored:
                print(f"⚠️ 检测到椭圆镜像变换！原始角度: {np.degrees(angle):.1f}°")

                # 半径计算修正（处理非均匀缩放）
                scale_x = entity.get('scale_x', 1.0)
                scale_y = entity.get('scale_y', 1.0)
                if scale_x != 1.0 or scale_y != 1.0:
                    import math
                    # 使用几何平均处理非均匀缩放
                    scale_factor = math.sqrt(abs(scale_x * scale_y))
                    a = abs(a * scale_factor)
                    b = abs(b * scale_factor)

                # 角度校正：反转角度方向
                angle = -angle

                # 参数角度校正
                if abs(end_param - start_param) < 2 * np.pi - 0.1:
                    # 非完整椭圆需要调整参数角度
                    original_start = start_param
                    original_end = end_param

                    # 反转参数角度方向并交换起止角度
                    start_param = 2 * np.pi - original_end
                    end_param = 2 * np.pi - original_start

                    print(f"椭圆弧参数调整: 起点={np.degrees(original_start):.1f}°→{np.degrees(start_param):.1f}°, "
                          f"终点={np.degrees(original_end):.1f}°→{np.degrees(end_param):.1f}°")

                print(f"椭圆变换后角度: {np.degrees(angle):.1f}°")

            # 创建椭圆（增强错误处理）
            try:
                # 确保参数有效
                if a <= 0 or b <= 0:
                    print(f"⚠️ 椭圆轴长无效: a={a}, b={b}")
                    return

                # 限制角度范围
                angle_deg = np.degrees(angle) % 360

                if abs(end_param - start_param) < 2 * np.pi - 0.1:  # 非完整椭圆
                    start_deg = np.degrees(start_param) % 360
                    end_deg = np.degrees(end_param) % 360

                    # 处理跨越角度
                    if end_deg < start_deg:
                        end_deg += 360

                    arc = Arc(center, 2*a, 2*b, angle=angle_deg,
                             theta1=start_deg, theta2=end_deg,
                             edgecolor=color, linewidth=linewidth, alpha=alpha,
                             fill=False)
                    ax.add_patch(arc)
                else:  # 完整椭圆
                    ellipse = MplEllipse(center, 2*a, 2*b, angle=angle_deg,
                                        edgecolor=color, fill=False,
                                        linewidth=linewidth, alpha=alpha)
                    ax.add_patch(ellipse)
            except Exception as e:
                print(f"⚠️ 椭圆绘制失败: {e}")
                print(f"   参数: 中心={center}, a={a:.2f}, b={b:.2f}, 角度={np.degrees(angle):.1f}°")
                # 降级处理：绘制圆形
                try:
                    avg_radius = (a + b) / 2
                    circle = Circle(center, avg_radius, edgecolor=color, fill=False,
                                  linewidth=linewidth, alpha=alpha)
                    ax.add_patch(circle)
                    print(f"   已降级为圆形，半径={avg_radius:.2f}")
                except Exception as e2:
                    print(f"   圆形降级也失败: {e2}")
        elif entity_type == 'SPLINE':
            # 优先使用拟合点，其次控制点
            points = []
            if 'fit_points' in entity and entity['fit_points']:
                points = entity['fit_points']
            elif 'control_points' in entity and entity['control_points']:
                points = entity['control_points']
            if points and len(points) >= 2:
                points = np.array(points)
                # 如果点数较多，直接连线；点数较少时用插值平滑
                if len(points) >= 4:
                    try:
                        from scipy.interpolate import splprep, splev
                        tck, _ = splprep([points[:,0], points[:,1]], s=0)
                        unew = np.linspace(0, 1, max(100, len(points)*10))
                        out = splev(unew, tck)
                        ax.plot(out[0], out[1], color=color, linewidth=linewidth, alpha=alpha)
                    except Exception:
                        # 没有scipy时直接折线
                        ax.plot(points[:,0], points[:,1], color=color, linewidth=linewidth, alpha=alpha)
                else:
                    ax.plot(points[:,0], points[:,1], color=color, linewidth=linewidth, alpha=alpha)

    def _get_entity_centroid(self, entity):
        """获取实体的质心坐标（增强数据类型检查）"""
        try:
            if 'points' in entity and entity['points']:
                points = entity['points']
                # 确保points是数值类型的列表
                if isinstance(points, list) and len(points) > 0:
                    # 检查并转换数据类型
                    numeric_points = []
                    for point in points:
                        if isinstance(point, (list, tuple)) and len(point) >= 2:
                            try:
                                numeric_point = [float(point[0]), float(point[1])]
                                numeric_points.append(numeric_point)
                            except (ValueError, TypeError):
                                print(f"⚠️ 跳过无效点: {point}")
                                continue

                    if numeric_points:
                        points_array = np.array(numeric_points, dtype=float)
                        centroid = np.mean(points_array, axis=0)
                        return tuple(centroid)

            elif 'center' in entity and entity['center']:
                center = entity['center']
                if isinstance(center, (list, tuple)) and len(center) >= 2:
                    try:
                        return (float(center[0]), float(center[1]))
                    except (ValueError, TypeError):
                        print(f"⚠️ 无效的center数据: {center}")

            elif 'position' in entity and entity['position']:
                position = entity['position']
                if isinstance(position, (list, tuple)) and len(position) >= 2:
                    try:
                        return (float(position[0]), float(position[1]))
                    except (ValueError, TypeError):
                        print(f"⚠️ 无效的position数据: {position}")

            elif 'def_points' in entity and entity['def_points']:
                def_points = entity['def_points']
                if isinstance(def_points, list):
                    numeric_points = []
                    for point in def_points:
                        if point is not None and isinstance(point, (list, tuple)) and len(point) >= 2:
                            try:
                                numeric_point = [float(point[0]), float(point[1])]
                                numeric_points.append(numeric_point)
                            except (ValueError, TypeError):
                                print(f"⚠️ 跳过无效def_point: {point}")
                                continue

                    if numeric_points:
                        points_array = np.array(numeric_points, dtype=float)
                        centroid = np.mean(points_array, axis=0)
                        return tuple(centroid)

        except Exception as e:
            print(f"⚠️ 计算实体质心失败: {e}")
            print(f"   实体类型: {entity.get('type', 'unknown')}")
            print(f"   实体数据: {entity}")

        return None

    def update_color_scheme(self, color_scheme):
        """更新配色方案（修复版 - 确保配色方案同步）"""
        if color_scheme:
            # 🔧 关键修复：完全替换配色方案，而不是仅更新部分
            self.color_scheme = color_scheme.copy()

            # 🔧 修复：同时更新current_color_scheme属性，确保所有颜色获取方法都使用新配色
            self.current_color_scheme = color_scheme.copy()

            # 更新背景色 - 只应用到CAD填充控制区域（右侧概览）
            if 'background' in color_scheme:
                # 只更新右侧的概览区域背景，不影响整个图像预览框
                self.ax_overview.set_facecolor(color_scheme['background'])
                # 保持左侧详细视图和整体图形的原始背景色
                # self.ax_detail.set_facecolor(color_scheme['background'])  # 注释掉
                # self.fig.patch.set_facecolor(color_scheme['background'])  # 注释掉

            # 🎨 通知显示控制器配色方案已更新
            if self.display_controller:
                try:
                    # 导入需要的类型
                    from display_controller import DataChangeType
                    self.display_controller.notify_data_change(
                        DataChangeType.COLOR_SCHEME,
                        color_scheme.copy(),
                        "CADVisualizer.update_color_scheme"
                    )
                except ImportError:
                    # 如果显示控制器模块不可用，静默忽略
                    pass

    def get_entity_color(self, entity, category_mapping=None):
        """根据实体类型和标签获取颜色（修复版 - 使用配色方案）"""
        # 🔧 修复：如果配色方案未设置，返回默认灰色
        if not self.color_scheme:
            return '#808080'

        # 如果实体有标签，使用标签对应的颜色
        if entity.get('label'):
            label = entity['label']
            return self.color_scheme.get(label, self.color_scheme.get('other', '#808080'))

        # 🔧 修复：如果没有标签，根据实体类型使用配色方案颜色而不是type_colors
        entity_type = entity.get('type', 'LINE')

        # 根据实体类型映射到配色方案
        if entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
            return self.color_scheme.get('wall', self.color_scheme.get('other', '#808080'))
        elif entity_type == 'INSERT':
            return self.color_scheme.get('furniture', self.color_scheme.get('other', '#808080'))
        elif entity_type in ['TEXT', 'MTEXT']:
            return self.color_scheme.get('text', self.color_scheme.get('other', '#808080'))
        elif entity_type == 'DIMENSION':
            return self.color_scheme.get('dimension', self.color_scheme.get('other', '#808080'))
        else:
            return self.color_scheme.get('other', '#808080')

    def _get_entity_color(self, entity):
        """获取实体颜色（简化版本 - 修复版）"""
        # 🔧 修复：如果配色方案未设置，返回默认灰色
        if not self.color_scheme:
            return '#808080'

        # 检查实体是否有标签
        if entity.get('label'):
            label = entity['label']
            # 🔧 修复：只使用配色方案中的颜色
            if label in self.color_scheme:
                return self.color_scheme[label]
            # 🔧 修复：如果配色方案中没有，使用other颜色
            else:
                return self.color_scheme.get('other', '#808080')

        # 检查实体图层
        layer = entity.get('layer', '')
        if layer:
            # 根据图层名称推断颜色
            layer_lower = layer.lower()
            if 'wall' in layer_lower:
                return self.color_scheme.get('wall', '#808080')
            elif 'door' in layer_lower or 'window' in layer_lower:
                return self.color_scheme.get('door_window', '#808080')
            elif 'railing' in layer_lower:
                return self.color_scheme.get('railing', '#808080')
            elif 'furniture' in layer_lower:
                return self.color_scheme.get('furniture', '#808080')

        # 检查实体是否在处理过程中
        if entity.get('processing', False):
            return self.color_scheme.get('processing_lines', '#808080')

        # 默认使用其他线条颜色
        return self.color_scheme.get('other_lines', self.color_scheme.get('other', '#808080'))

    def _get_group_bbox(self, group):
        """获取组的边界框"""
        if not group:
            return None

        min_x = float('inf')
        min_y = float('inf')
        max_x = float('-inf')
        max_y = float('-inf')

        for entity in group:
            centroid = self._get_entity_centroid(entity)
            if centroid:
                min_x = min(min_x, centroid[0])
                min_y = min(min_y, centroid[1])
                max_x = max(max_x, centroid[0])
                max_y = max(max_y, centroid[1])

        if min_x != float('inf'):
            return (min_x, min_y, max_x, max_y)
        return None

    def update_canvas(self, canvas):
        """更新画布"""
        if canvas:
            canvas.figure = self.fig
            canvas.draw()

    def get_figure(self):
        """获取图形对象"""
        return self.fig

    def set_color_scheme(self, color_scheme):
        """设置配色系统"""
        self.current_color_scheme = color_scheme
        # 🔧 修复：同时更新color_scheme属性，确保颜色获取方法使用正确的配色
        self.color_scheme = color_scheme.copy() if color_scheme else {}
        print(f"🎨 可视化器配色系统已设置: {len(color_scheme) if color_scheme else 0} 种颜色")

        # 🔧 修复：配色更改后强制刷新界面
        self._refresh_after_color_change()

    def _refresh_after_color_change(self):
        """配色更改后刷新界面"""
        try:
            print(f"🔄 配色更改后刷新界面...")

            # 清除当前绘图
            if hasattr(self, 'ax_overview') and self.ax_overview:
                self.ax_overview.clear()
            if hasattr(self, 'ax_detail') and self.ax_detail:
                self.ax_detail.clear()

            # 如果有缓存的数据，重新绘制
            if hasattr(self, '_last_overview_data') and self._last_overview_data:
                print(f"  🔄 使用缓存数据重新绘制全图概览...")
                data = self._last_overview_data

                # 🔧 修复：检查是否应用了性能优化
                if hasattr(self, '_performance_fix_applied') and self._performance_fix_applied:
                    # 性能优化版本：只传递前3个参数，其他作为kwargs
                    self.visualize_overview(
                        data.get('all_entities', []),
                        data.get('current_group_entities', []),
                        data.get('labeled_entities', []),
                        processor=data.get('processor'),
                        current_group_index=data.get('current_group_index'),
                        wall_fills=data.get('wall_fills'),
                        wall_fill_processor=data.get('wall_fill_processor'),
                        hidden_groups=data.get('hidden_groups', []),
                        all_groups=data.get('all_groups', []),
                        groups_info=data.get('groups_info', [])
                    )
                else:
                    # 原始版本：传递所有位置参数
                    self.visualize_overview(
                        data.get('all_entities', []),
                        data.get('current_group_entities', []),
                        data.get('labeled_entities', []),
                        data.get('processor'),
                        data.get('current_group_index'),
                        data.get('wall_fills'),
                        data.get('wall_fill_processor'),
                        data.get('hidden_groups', []),
                        data.get('all_groups', []),
                        data.get('groups_info', [])
                    )
            else:
                # 如果没有缓存数据，只刷新画布
                print(f"  🔄 刷新画布...")
                if hasattr(self, 'canvas') and self.canvas:
                    self.canvas.draw()

            print(f"  ✅ 界面刷新完成")

        except Exception as e:
            print(f"  ❌ 界面刷新失败: {e}")

    def clear_plots(self):
        """清除所有绘图"""
        self.ax_detail.clear()
        self.ax_overview.clear()
        self.fig.tight_layout()

    def clear_all(self):
        """清除所有绘图（别名方法）"""
        self.clear_plots()

    def draw_entities(self, entities, labeled_entities=None, current_group_entities=None,
                     all_groups=None, groups_info=None, processor=None):
        """绘制实体列表（修复版 - 使用正确的颜色逻辑）"""
        if not entities:
            print("⚠️ 没有实体数据需要绘制")
            return

        print(f"🎨 绘制实体列表，共 {len(entities)} 个实体")

        # 清除详细视图
        self.ax_detail.clear()
        self.ax_detail.set_aspect('equal')
        self.ax_detail.grid(True, linestyle='--', alpha=0.3)

        # 绘制每个实体
        for i, entity in enumerate(entities):
            try:
                # 使用基于组信息的颜色获取
                group_info = self._find_entity_group_info_simple(entity, all_groups, groups_info)
                color = self._get_group_color(group_info)
                alpha = 0.8
                linewidth = 1.0

                # 绘制实体
                self._draw_entity(entity, color, linewidth, alpha, self.ax_detail)

            except Exception as e:
                print(f"绘制实体 {i+1} 失败: {e}")

        # 设置坐标范围
        try:
            min_x, min_y, max_x, max_y = self._calculate_entities_bounds(entities)
            if min_x != float('inf'):
                margin = max((max_x - min_x) * 0.1, (max_y - min_y) * 0.1, 10)
                self.ax_detail.set_xlim(min_x - margin, max_x + margin)
                self.ax_detail.set_ylim(min_y - margin, max_y + margin)
        except Exception as e:
            print(f"设置坐标范围失败: {e}")

        self.ax_detail.set_title('详细视图', fontproperties=self.chinese_font)
        print("✅ 实体列表绘制完成")

    def draw_groups(self, groups, groups_info=None, labeled_entities=None, processor=None):
        """绘制分组列表（修复版 - 使用正确的颜色逻辑）"""
        if not groups:
            print("⚠️ 没有分组数据需要绘制")
            return



        # 清除概览视图
        self.ax_overview.clear()
        self.ax_overview.set_aspect('equal')
        self.ax_overview.grid(True, linestyle='--', alpha=0.3)

        all_entities = []

        # 绘制每个组
        for group_idx, group in enumerate(groups):
            if not group:
                continue

            try:
                # 🔧 修复：使用组状态信息获取正确的颜色
                if groups_info and group_idx < len(groups_info):
                    group_info = groups_info[group_idx]
                    group_color = group_info.get('display_color', '#808080')
                    group_label = group_info.get('label', 'unknown')
                    print(f"  组{group_idx+1} ({group_label}): 使用颜色 {group_color}")
                else:
                    # 回退到默认颜色
                    colors = plt.cm.tab10(np.linspace(0, 1, len(groups)))
                    group_color = colors[group_idx % len(colors)]
                    group_label = f'组{group_idx+1}'

                # 绘制组中的每个实体
                for entity in group:
                    if isinstance(entity, dict):  # 确保是有效的实体
                        try:
                            # 使用基于组信息的颜色获取
                            group_info = groups_info[group_idx] if groups_info and group_idx < len(groups_info) else None
                            color = self._get_group_color(group_info)
                            alpha = 0.7
                            linewidth = 1.5

                            self._draw_entity(entity, color, linewidth, alpha, self.ax_overview)
                            all_entities.append(entity)
                        except Exception as e:
                            print(f"  绘制实体失败: {e}")

                # 🔧 需求2修复：不显示组标签（图层信息）
                # 注释掉组标签显示，避免在图上显示图层信息
                # group_center = self._get_group_center(group)
                # if group_center:
                #     # 🔧 修复：使用配色系统获取文字和背景颜色
                #     text_color = self.color_scheme.get('text', '#000000')
                #     bg_color = self.color_scheme.get('background', '#FFFFFF')
                #     self.ax_overview.text(group_center[0], group_center[1],
                #                         group_label,
                #                         fontsize=8, color=text_color,
                #                         bbox=dict(facecolor=bg_color, alpha=0.8, edgecolor=group_color, pad=2),
                #                         fontproperties=self.chinese_font,
                #                         ha='center', va='center')

            except Exception as e:
                print(f"绘制组 {group_idx+1} 失败: {e}")

        # 设置坐标范围
        if all_entities:
            try:
                min_x, min_y, max_x, max_y = self._calculate_entities_bounds(all_entities)
                if min_x != float('inf'):
                    margin = max((max_x - min_x) * 0.1, (max_y - min_y) * 0.1, 10)
                    self.ax_overview.set_xlim(min_x - margin, max_x + margin)
                    self.ax_overview.set_ylim(min_y - margin, max_y + margin)
            except Exception as e:
                print(f"设置坐标范围失败: {e}")

        self.ax_overview.set_title('全图概览', fontproperties=self.chinese_font)
        print("✅ 分组列表绘制完成")

    def _get_group_center(self, group):
        """获取组的中心点"""
        if not group:
            return None

        x_coords = []
        y_coords = []

        for entity in group:
            if isinstance(entity, dict):
                centroid = self._get_entity_centroid(entity)
                if centroid:
                    x_coords.append(centroid[0])
                    y_coords.append(centroid[1])

        if x_coords and y_coords:
            return (sum(x_coords) / len(x_coords), sum(y_coords) / len(y_coords))

        return None

    def set_global_wall_fills(self, wall_fills, wall_fill_processor):
        """设置全局墙体填充数据（用于V2版本）"""
        self._global_wall_fills = wall_fills
        self._global_wall_fill_processor = wall_fill_processor

    def clear_global_wall_fills(self):
        """清除全局墙体填充数据"""
        if hasattr(self, '_global_wall_fills'):
            delattr(self, '_global_wall_fills')
        if hasattr(self, '_global_wall_fill_processor'):
            delattr(self, '_global_wall_fill_processor')

    def _check_arc_mirrored(self, entity):
        """检查圆弧是否被镜像（改进版：基于缩放因子）"""
        try:
            # 方法1：检查实体是否有镜像标记
            if 'mirrored' in entity:
                return entity['mirrored']

            # 方法2：基于缩放因子检测镜像（推荐方法）
            scale_x, scale_y = self._extract_scale_factors(entity)

            # 通过缩放因子符号检测镜像
            mirror_x = 1 if scale_x >= 0 else -1
            mirror_y = 1 if scale_y >= 0 else -1
            mirror_effect = mirror_x * mirror_y

            # mirror_effect = -1 表示奇数次镜像（需要角度反转）
            if mirror_effect < 0:

                return True

            # 方法3：检查变换矩阵是否包含镜像
            if 'transform_matrix' in entity:
                matrix = entity['transform_matrix']
                # 检查矩阵的行列式，负值表示镜像
                if len(matrix) >= 2 and len(matrix[0]) >= 2:
                    det = matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0]
                    return det < 0

            # 方法4：检查缩放变换
            if 'scale' in entity:
                scale = entity['scale']
                if isinstance(scale, (list, tuple)) and len(scale) >= 2:
                    scale_x, scale_y = scale[0], scale[1]
                    mirror_x = 1 if scale_x >= 0 else -1
                    mirror_y = 1 if scale_y >= 0 else -1
                    mirror_effect = mirror_x * mirror_y
                    return mirror_effect < 0

            return False

        except Exception as e:
            print(f"检查圆弧镜像状态失败: {e}")
            return False

    def _extract_scale_factors(self, entity):
        """提取实体的缩放因子"""
        try:
            scale_x = 1.0
            scale_y = 1.0

            # 方法1：直接从实体属性获取
            if 'scale_x' in entity and 'scale_y' in entity:
                scale_x = entity['scale_x']
                scale_y = entity['scale_y']
            elif 'scale' in entity:
                scale = entity['scale']
                if isinstance(scale, (list, tuple)) and len(scale) >= 2:
                    scale_x, scale_y = scale[0], scale[1]
                elif isinstance(scale, (int, float)):
                    scale_x = scale_y = scale

            # 方法2：从变换矩阵提取
            elif 'transform_matrix' in entity:
                matrix = entity['transform_matrix']
                if len(matrix) >= 2 and len(matrix[0]) >= 2:
                    # 提取缩放因子（矩阵对角元素）
                    scale_x = matrix[0][0]
                    scale_y = matrix[1][1]

            # 方法3：从块引用信息获取
            elif 'block_scale' in entity:
                block_scale = entity['block_scale']
                if isinstance(block_scale, (list, tuple)) and len(block_scale) >= 2:
                    scale_x, scale_y = block_scale[0], block_scale[1]
                elif isinstance(block_scale, (int, float)):
                    scale_x = scale_y = block_scale

            return scale_x, scale_y

        except Exception as e:
            print(f"提取缩放因子失败: {e}")
            return 1.0, 1.0

    def _fix_mirrored_arc_angles(self, start_angle, end_angle):
        """修复镜像圆弧的角度（改进版：基于几何一致性）"""
        try:
            # 角度校正：反转角度方向并交换起止角度
            # 这确保镜像后的圆弧保持正确的几何方向和位置

            # 1. 反转角度方向（顺时针↔逆时针）
            fixed_start = 360 - start_angle
            fixed_end = 360 - end_angle

            # 2. 交换起止角度保持几何一致性
            fixed_start, fixed_end = fixed_end, fixed_start

            # 3. 标准化角度到0-360度范围
            fixed_start = fixed_start % 360
            fixed_end = fixed_end % 360

            # 4. 处理角度跨越问题
            if fixed_end < fixed_start:
                # 如果结束角度小于开始角度，可能需要调整
                angle_span = (start_angle - end_angle) % 360
                if angle_span > 180:
                    # 大角度跨越，调整结束角度
                    fixed_end += 360

            return fixed_start, fixed_end

        except Exception as e:
            print(f"修复镜像圆弧角度失败: {e}")
            return start_angle, end_angle

    def _calculate_entities_bounds(self, entities):
        """计算所有实体的坐标边界（修复版）"""
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')

        for entity in entities:
            coords = self._get_entity_coordinates_fixed(entity)

            for x, y in coords:
                if isinstance(x, (int, float)) and isinstance(y, (int, float)):
                    min_x = min(min_x, x)
                    max_x = max(max_x, x)
                    min_y = min(min_y, y)
                    max_y = max(max_y, y)

        # 如果没有有效坐标，返回默认范围
        if min_x == float('inf'):
            return 0, 0, 100, 100

        return min_x, min_y, max_x, max_y

    def _get_entity_coordinates_fixed(self, entity):
        """获取实体坐标（修复版）"""
        coords = []
        entity_type = entity.get('type', '')

        try:
            if entity_type == 'LINE':
                # 支持多种LINE数据格式
                if 'start' in entity and 'end' in entity:
                    start = entity.get('start', [0, 0])
                    end = entity.get('end', [0, 0])
                    coords.extend([start, end])
                elif 'points' in entity:
                    points = entity.get('points', [])
                    if len(points) >= 2:
                        coords.extend(points)
                    else:
                        coords.extend([[0, 0], [0, 0]])
                else:
                    coords.extend([[0, 0], [0, 0]])

            elif entity_type == 'CIRCLE':
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                # 添加圆的边界点
                coords.extend([
                    [center[0] - radius, center[1] - radius],
                    [center[0] + radius, center[1] + radius],
                    [center[0], center[1]]  # 中心点
                ])

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                coords.extend(points)

            elif entity_type == 'ARC':
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                coords.extend([
                    [center[0] - radius, center[1] - radius],
                    [center[0] + radius, center[1] + radius],
                    [center[0], center[1]]
                ])

            elif entity_type == 'INSERT':
                insertion_point = entity.get('insertion_point', [0, 0])
                coords.append(insertion_point)

            else:
                # 尝试通用坐标获取
                if 'start' in entity and 'end' in entity:
                    coords.extend([entity['start'], entity['end']])
                elif 'center' in entity:
                    coords.append(entity['center'])
                elif 'points' in entity:
                    coords.extend(entity['points'])
                elif 'insertion_point' in entity:
                    coords.append(entity['insertion_point'])

        except Exception as e:
            print(f"    ⚠️ 获取实体坐标失败: {e}")
            coords = [[0, 0]]  # 返回默认坐标

        return coords

    def _get_entity_color_fixed(self, entity, labeled_entities=None):
        """获取实体颜色（修复版 - 使用配色方案）"""

        # 🔑 关键检查1：实体是否有标签
        if entity.get('label'):
            # 🔧 修复：使用配色方案而不是category_colors
            label = entity.get('label')
            color = self.color_scheme.get(label, self.color_scheme.get('other', '#808080'))
            return color, 0.9, "实体标签"

        # 🔑 关键检查2：自动标注实体
        elif entity.get('auto_labeled') and entity.get('label'):
            # 🔧 修复：使用配色方案而不是category_colors
            label = entity.get('label')
            color = self.color_scheme.get(label, self.color_scheme.get('other', '#808080'))
            return color, 0.8, "自动标注标签"

        # 🔑 关键检查3：在已标注实体列表中（修复unhashable dict错误）
        elif labeled_entities and self._is_entity_in_labeled_list(entity, labeled_entities):
            # 🔧 修复：使用配色方案中的已标注颜色
            labeled_color = self.color_scheme.get('labeled', '#00FF00')
            return labeled_color, 0.8, "已标注实体列表"

        else:
            # 🔧 修复：使用配色方案中的未标注颜色
            unlabeled_color = self.color_scheme.get('unlabeled', '#D3D3D3')
            return unlabeled_color, 0.7, "默认颜色"



    def _find_entity_group_info(self, entity, all_groups, groups_info, processor):
        """🔧 新增：查找实体所属的组信息（与替代视图一致的逻辑）"""
        try:
            if not all_groups:
                return None

            for group_index, group in enumerate(all_groups):
                # 获取组中的实体列表
                entities = group if isinstance(group, list) else group.get('entities', [])

                # 检查实体是否在这个组中
                if self._is_entity_in_group_safe(entity, entities):
                    # 获取对应的组信息
                    group_info = None
                    if groups_info and group_index < len(groups_info):
                        group_info = groups_info[group_index]

                    return (group_index, group_info)

            return None

        except Exception as e:
            print(f"⚠️ 查找实体组信息失败: {e}")
            return None

    def _get_alternative_entity_color_for_overview(self, entity, group_info):
        """🔧 新增：使用与替代视图一致的颜色获取逻辑"""
        try:
            # 🔧 与替代视图一致的优先级顺序

            # 优先级1：当前组（最高优先级）
            if group_info and group_info.get('is_current_group'):
                return self.color_scheme.get('current_group', '#FF0000') if self.color_scheme else '#FF0000'

            # 优先级2：正在标注状态
            if group_info:
                status = group_info.get('status', '').lower().strip()
                if status in ['labeling', 'pending']:
                    return self.color_scheme.get('current_group', '#FF0000') if self.color_scheme else '#FF0000'

            # 优先级3：已标注标签（labeled、auto_labeled、relabeled）
            if group_info:
                status = group_info.get('status', '').lower().strip()
                label = group_info.get('label', '').strip()

                if status in ['labeled', 'auto_labeled', 'relabeled'] and label and label != '未标注':
                    # 使用与替代视图一致的标签颜色映射
                    color_key = self._map_label_to_color_key(label.lower().strip())
                    return self._get_color_from_scheme(color_key)

            # 优先级4：未标注状态
            return self.color_scheme.get('unlabeled', '#808080') if self.color_scheme else '#808080'

        except Exception as e:
            print(f"⚠️ 获取替代视图颜色失败: {e}")
            return '#808080'





    def _get_entity_color_by_type_and_layer(self, entity):
        """根据实体类型和图层获取颜色（备用方案）"""
        try:
            # 🔧 修复：如果配色方案未设置，返回默认灰色
            if not self.color_scheme:
                return '#808080'

            layer_name = str(entity.get('layer', '')).lower()
            entity_type = entity.get('type', 'LINE')

            # 🔧 修复：为未标注实体使用配色系统的颜色
            # 根据图层名称判断类型，使用配色系统的颜色
            if any(keyword in layer_name for keyword in ['a-wall', 'wall', '墙']):
                return self.color_scheme.get('wall', self.color_scheme.get('other', '#808080'))  # 墙体颜色
            elif any(keyword in layer_name for keyword in ['a-door', 'a-window', 'door', 'window', '门', '窗']):
                return self.color_scheme.get('door_window', self.color_scheme.get('other', '#808080'))  # 门窗颜色
            elif any(keyword in layer_name for keyword in ['a-column', 'column', '柱']):
                return self.color_scheme.get('column', self.color_scheme.get('other', '#808080'))  # 柱子颜色
            elif any(keyword in layer_name for keyword in ['a-furniture', 'furniture', '家具']):
                return self.color_scheme.get('furniture', self.color_scheme.get('other', '#808080'))  # 家具颜色
            else:
                # 🔧 修复：根据实体类型使用配色系统的颜色，避免硬编码
                entity_type = entity.get('type', 'UNKNOWN')

                # 根据实体类型映射到配色系统的类别
                if entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                    return self.color_scheme.get('other_lines', self.color_scheme.get('other', '#808080'))  # 线条类
                elif entity_type in ['CIRCLE', 'ARC', 'ELLIPSE']:
                    return self.color_scheme.get('other', '#808080')  # 圆弧类
                elif entity_type in ['INSERT']:
                    return self.color_scheme.get('furniture', self.color_scheme.get('other', '#808080'))  # 块插入（通常是家具）
                elif entity_type in ['TEXT', 'MTEXT']:
                    return self.color_scheme.get('text', self.color_scheme.get('other', '#808080'))  # 文字类
                elif entity_type in ['DIMENSION']:
                    return self.color_scheme.get('dimension', self.color_scheme.get('other', '#808080'))  # 标注类
                else:
                    return self.color_scheme.get('other', '#808080')  # 其他类型

        except Exception:
            return '#808080'

    def _draw_entity_enhanced(self, entity, color, linewidth, alpha, ax):
        """绘制实体（增强版）"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE':
                # 🔑 修复：支持多种数据格式
                if 'start_x' in entity and 'start_y' in entity and 'end_x' in entity and 'end_y' in entity:
                    # 格式1：start_x, start_y, end_x, end_y字段
                    x = [entity['start_x'], entity['end_x']]
                    y = [entity['start_y'], entity['end_y']]
                elif 'start' in entity and 'end' in entity:
                    # 格式2：start, end数组
                    start = entity.get('start', [0, 0])
                    end = entity.get('end', [0, 0])
                    x = [start[0], end[0]]
                    y = [start[1], end[1]]
                elif 'points' in entity:
                    # 格式3：points数组
                    points = entity['points']
                    if len(points) >= 2:
                        x = [points[0][0], points[1][0]]
                        y = [points[0][1], points[1][1]]
                    else:
                        return False
                else:
                    print(f"  ⚠️ LINE实体缺少坐标信息: {list(entity.keys())}")
                    return False

                ax.plot(x, y, color=color, alpha=alpha, linewidth=linewidth)
                return True

            elif entity_type == 'CIRCLE':
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                import matplotlib.patches as patches
                circle = patches.Circle(center, radius, fill=False,
                                      color=color, alpha=alpha, linewidth=linewidth)
                ax.add_patch(circle)
                return True

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                if len(points) >= 2:
                    xs = [p[0] for p in points]
                    ys = [p[1] for p in points]
                    ax.plot(xs, ys, color=color, alpha=alpha, linewidth=linewidth)
                    return True

            elif entity_type == 'ARC':
                # 简化绘制弧形为圆
                center = entity.get('center', [0, 0])
                radius = entity.get('radius', 0)
                import matplotlib.patches as patches
                circle = patches.Circle(center, radius, fill=False,
                                      color=color, alpha=alpha, linewidth=linewidth)
                ax.add_patch(circle)
                return True

            else:
                # 尝试使用原有的绘制方法
                self._draw_entity(entity, color, linewidth, alpha, ax)
                return True

            return False

        except Exception as e:
            print(f"      绘制实体失败: {e}")
            return False

    def _highlight_current_group(self, group_entities):
        """高亮当前组（修复版）"""
        try:
            for entity in group_entities:
                coords = self._get_entity_coordinates_fixed(entity)
                for x, y in coords:
                    self.ax_overview.plot(x, y, 'ro', markersize=8, alpha=0.7)  # 红色圆点高亮
        except Exception as e:
            print(f"    高亮当前组失败: {e}")

    def _draw_group_boundaries_enhanced(self, all_groups, _groups_info, current_group_index, _processor, hidden_groups=None):
        """绘制组边界框和状态标识（增强版）"""
        try:
            print(f"  - 🎨 开始绘制组边界框: {len(all_groups)} 个组")

            for i, group in enumerate(all_groups):
                if not group:
                    continue

                # 跳过隐藏组
                if hidden_groups and i in hidden_groups:
                    continue

                # 只显示正在标注的组的边界框和标签
                if i != current_group_index:
                    continue

                # 获取组的边界框
                bbox = self._get_group_bbox(group)
                if not bbox:
                    continue

                min_x, min_y, max_x, max_y = bbox

                # 正在标注组的样式
                border_color = '#FF0000'  # 红色
                border_style = '-'        # 实线
                border_width = 3.0        # 粗线

                # 绘制边界框
                import matplotlib.patches as patches
                rect = patches.Rectangle(
                    (min_x, min_y), max_x - min_x, max_y - min_y,
                    linewidth=border_width, edgecolor=border_color,
                    facecolor='none', linestyle=border_style, alpha=0.8
                )
                self.ax_overview.add_patch(rect)

                # 添加组标签
                center_x = (min_x + max_x) / 2
                center_y = (min_y + max_y) / 2

                # 正在标注组的标签样式
                # 🔧 修复：使用配色系统获取标签颜色
                label_bg = self.color_scheme.get('labeling', '#FFA500')
                label_color = self.color_scheme.get('highlight', '#FF0000')
                label_text = f'组{i+1}[标注中]'

                # 绘制标签
                self.ax_overview.text(
                    center_x, center_y, label_text,
                    fontproperties=self.chinese_font, fontsize=10,
                    color=label_color, weight='bold',
                    bbox=dict(facecolor=label_bg, alpha=0.8, edgecolor=border_color, linewidth=1),
                    ha='center', va='center'
                )

            print(f"  - ✅ 组边界框绘制完成")

        except Exception as e:
            print(f"  - ❌ 绘制组边界框失败: {e}")
            import traceback
            traceback.print_exc()

    def _highlight_current_annotation_group(self, group_entities, group_index):
        """高亮当前正在标注的组（增强版）"""
        try:
            print(f"    - 🔴 高亮标注组{group_index + 1}: {len(group_entities)} 个实体")

            # 获取组的边界框
            bbox = self._get_group_bbox(group_entities)
            if bbox:
                min_x, min_y, max_x, max_y = bbox

                # 绘制闪烁效果的边界框
                import matplotlib.patches as patches

                # 外层红色粗边框
                outer_rect = patches.Rectangle(
                    (min_x - 5, min_y - 5), (max_x - min_x) + 10, (max_y - min_y) + 10,
                    linewidth=4.0, edgecolor='#FF0000', facecolor='none',
                    linestyle='-', alpha=0.9
                )
                self.ax_overview.add_patch(outer_rect)

                # 内层黄色细边框
                inner_rect = patches.Rectangle(
                    (min_x - 2, min_y - 2), (max_x - min_x) + 4, (max_y - min_y) + 4,
                    linewidth=2.0, edgecolor='#FFFF00', facecolor='none',
                    linestyle='-', alpha=0.7
                )
                self.ax_overview.add_patch(inner_rect)

                # 在组的四个角添加标记点
                corner_size = 8
                corners = [
                    (min_x, min_y), (max_x, min_y),  # 下方两角
                    (min_x, max_y), (max_x, max_y)   # 上方两角
                ]

                for x, y in corners:
                    self.ax_overview.plot(x, y, 'ro', markersize=corner_size, alpha=0.9)
                    self.ax_overview.plot(x, y, 'yo', markersize=corner_size-2, alpha=0.7)

            print(f"    - ✅ 标注组高亮完成")

        except Exception as e:
            print(f"    - ❌ 高亮标注组失败: {e}")

    def _create_enhanced_legend(self, all_entities, group_status_stats, _current_group_index):
        """创建增强版图例（包含组状态和实体类别）"""
        try:
            import matplotlib.patches as mpatches
            legend_patches = []

            # 🔑 第一部分：组状态图例
            if any(count > 0 for count in group_status_stats.values()):
                # 添加组状态标题
                # 🔧 修复：使用配色系统获取分隔符颜色
                separator_color = self.color_scheme.get('background', '#FFFFFF')
                legend_patches.append(mpatches.Patch(color=separator_color, label='━━ 组状态 ━━'))

                # 当前标注组
                if group_status_stats.get('current', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#FF0000', label=f'● 正在标注 ({group_status_stats["current"]}个实体)'
                    ))

                # 已手动标注组
                if group_status_stats.get('labeled', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#00AA00', label=f'● 已标注 ({group_status_stats["labeled"]}个实体)'
                    ))

                # 自动标注组
                if group_status_stats.get('auto_labeled', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#0066CC', label=f'● 自动标注 ({group_status_stats["auto_labeled"]}个实体)'
                    ))

                # 未标注组
                if group_status_stats.get('unlabeled', 0) > 0:
                    legend_patches.append(mpatches.Patch(
                        color='#D3D3D3', label=f'○ 未标注 ({group_status_stats["unlabeled"]}个实体)'
                    ))

            # 🔑 第二部分：实体类别图例
            existing_categories = set()
            for entity in all_entities:
                label = entity.get('label')
                # 🔧 修复：检查配色方案而不是category_colors
                if label and label in self.color_scheme:
                    existing_categories.add(label)

            if existing_categories:
                # 添加类别标题
                # 🔧 修复：使用配色系统获取分隔符颜色
                separator_color = self.color_scheme.get('background', '#FFFFFF')
                legend_patches.append(mpatches.Patch(color=separator_color, label='━━ 实体类别 ━━'))

                # 类别名称映射（简化版，避免字体问题）
                category_names = {
                    'wall': '■ 墙体',
                    'door': '□ 门',
                    'door_window': '□ 门窗',
                    'window': '□ 窗户',
                    'railing': '▬ 栏杆',
                    'furniture': '▪ 家具',
                    'bed': '▪ 床',
                    'sofa': '▪ 沙发',
                    'cabinet': '▪ 柜子',
                    'dining_table': '▪ 餐桌',
                    'appliance': '▫ 家电',
                    'stair': '▲ 楼梯',
                    'elevator': '▲ 电梯',
                    'dimension': '─ 标注',
                    'room_label': '○ 房间',
                    'column': '● 柱子',
                    'title_block': '□ 图框',
                    'other': '? 其他'
                }

                # 按字母顺序排序类别
                sorted_categories = sorted(existing_categories)
                for cat in sorted_categories:
                    # 🔧 修复：使用配色方案而不是category_colors
                    color = self.color_scheme.get(cat, self.color_scheme.get('other', '#808080'))
                    legend_patches.append(mpatches.Patch(
                        color=color,
                        label=category_names.get(cat, cat)
                    ))

            # 显示图例
            if legend_patches:
                # 分两列显示图例以节省空间
                ncol = 2 if len(legend_patches) > 8 else 1
                legend = self.ax_overview.legend(
                    handles=legend_patches,
                    loc='upper right',
                    fontsize=8,
                    prop=self.chinese_font,
                    ncol=ncol,
                    framealpha=0.9,
                    fancybox=True,
                    shadow=True
                )

                # 设置图例边框
                # 🔧 修复：使用配色系统获取图例背景和边框颜色
                legend_bg = self.color_scheme.get('background', '#FFFFFF')
                legend_edge = self.color_scheme.get('text_secondary', '#808080')
                legend.get_frame().set_facecolor(legend_bg)
                legend.get_frame().set_edgecolor(legend_edge)
                legend.get_frame().set_linewidth(1)

                print(f"  - ✅ 增强图例创建完成: {len(legend_patches)} 个项目")

        except Exception as e:
            print(f"  - ❌ 创建增强图例失败: {e}")
            import traceback
            traceback.print_exc()

    def update_color_index(self, color_index_data):
        """🔧 问题1修复：更新颜色索引以显示所有使用的颜色"""
        try:
            print(f"🎨 更新颜色索引，包含 {len(color_index_data)} 种颜色")

            # 清空颜色索引图区域
            self.ax_color_legend.clear()
            self.ax_color_legend.axis('off')

            # 收集颜色信息
            color_info = []
            for label, info in color_index_data.items():
                color = info.get('color', '#808080')
                count = info.get('count', 0)
                color_info.append((color, f'{label} ({count}个)', 'custom'))

            # 如果没有颜色信息，显示提示
            if not color_info:
                self.ax_color_legend.text(0.5, 0.5, '暂无颜色信息',
                                        ha='center', va='center',
                                        fontproperties=self.chinese_font, fontsize=10)
                return

            # 🔧 优化：改进布局算法，适应组状态和实体类别双重信息
            total_items = len(color_info)

            # 🔧 优化：根据项目数量动态调整列数，避免过度拥挤
            if total_items <= 4:
                cols = 2  # 少量项目使用2列，便于阅读
            elif total_items <= 8:
                cols = 3  # 中等数量使用3列
            else:
                cols = 4  # 大量项目最多4列

            rows = (total_items + cols - 1) // cols  # 向上取整

            # 🔧 优化：分离组状态和实体类别，分区域显示
            status_items = [item for item in color_info if item[2] == 'status']
            type_items = [item for item in color_info if item[2] == 'type']

            # 绘制颜色索引
            current_y = 0.95  # 从顶部开始

            # 🔧 优化：先显示组状态（更重要的信息）
            if status_items:
                for i, (color, label, _) in enumerate(status_items):
                    col = i % cols
                    if col == 0 and i > 0:  # 换行
                        current_y -= 0.25

                    # 计算位置
                    x = col / cols + 0.05

                    # 🔧 优化：调整颜色方块大小，适应紧凑布局
                    rect_width = 0.04
                    rect_height = 0.15

                    # 创建颜色方块
                    from matplotlib.patches import Rectangle
                    edge_color = self.color_scheme.get('text', '#000000')
                    rect = Rectangle((x, current_y - rect_height/2), rect_width, rect_height,
                                   facecolor=color, edgecolor=edge_color, linewidth=0.5)
                    self.ax_color_legend.add_patch(rect)

                    # 🔧 优化：缩短标签文字，去除冗余信息
                    short_label = label.replace('个实体', '').replace('个组', '')
                    text_x = x + rect_width + 0.01
                    self.ax_color_legend.text(text_x, current_y, short_label,
                                            ha='left', va='center',
                                            fontproperties=self.chinese_font,
                                            fontsize=7)

                # 为类别信息预留空间
                if status_items:
                    current_y -= 0.3

            # 🔧 优化：然后显示实体类别（次要信息）
            if type_items:
                for i, (color, label, _) in enumerate(type_items):
                    col = i % cols
                    if col == 0 and i > 0:  # 换行
                        current_y -= 0.25

                    # 计算位置
                    x = col / cols + 0.05

                    # 绘制颜色方块
                    rect_width = 0.04
                    rect_height = 0.15

                    # 创建颜色方块
                    from matplotlib.patches import Rectangle
                    edge_color = self.color_scheme.get('text', '#000000')
                    rect = Rectangle((x, current_y - rect_height/2), rect_width, rect_height,
                                   facecolor=color, edgecolor=edge_color, linewidth=0.5)
                    self.ax_color_legend.add_patch(rect)

                    # 添加文字标签
                    text_x = x + rect_width + 0.01
                    self.ax_color_legend.text(text_x, current_y, label,
                                            ha='left', va='center',
                                            fontproperties=self.chinese_font,
                                            fontsize=7)

            # 🔧 优化：去除标题，直接显示内容，节省空间
            # 不再显示"颜色索引"标题，让内容占据更多空间

            # 设置坐标范围
            self.ax_color_legend.set_xlim(0, 1)
            self.ax_color_legend.set_ylim(0, 1)

            # 刷新画布
            if hasattr(self, 'fig') and self.fig.canvas:
                self.fig.canvas.draw_idle()

            print(f"✅ 颜色索引更新完成: {total_items} 个颜色项")

        except Exception as e:
            print(f"❌ 更新颜色索引失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_color_index_chart(self, color_stats, group_status_stats):
        """创建颜色索引图，显示所有使用的颜色及其含义"""
        try:
            print(f"🎨 开始创建颜色索引图")

            # 清空颜色索引图区域
            self.ax_color_legend.clear()
            self.ax_color_legend.axis('off')

            # 收集所有颜色信息
            color_info = []

            # 🔑 第一部分：组状态颜色
            if group_status_stats.get('current', 0) > 0:
                color_info.append(('#FF0000', f'正在标注 ({group_status_stats["current"]}个实体)', 'status'))

            if group_status_stats.get('labeled', 0) > 0:
                color_info.append(('#00AA00', f'已标注 ({group_status_stats["labeled"]}个实体)', 'status'))

            if group_status_stats.get('auto_labeled', 0) > 0:
                color_info.append(('#0066CC', f'自动标注 ({group_status_stats["auto_labeled"]}个实体)', 'status'))

            if group_status_stats.get('unlabeled', 0) > 0:
                color_info.append(('#A0A0A0', f'未标注 ({group_status_stats["unlabeled"]}个实体)', 'status'))

            # 🔑 第二部分：实体类型颜色（从配色系统获取）
            if hasattr(self, 'current_color_scheme') and self.current_color_scheme:
                # 常见的实体类型
                common_types = [
                    ('wall', '墙体'),
                    ('door_window', '门窗'),
                    ('furniture', '家具'),
                    ('column', '柱子'),
                    ('stair', '楼梯'),
                    ('other', '其他')
                ]

                for type_key, type_name in common_types:
                    if type_key in self.current_color_scheme:
                        color = self.current_color_scheme[type_key]
                        # 检查这个颜色是否在当前图中被使用
                        if any(color.upper() in str(stats_color).upper() for stats_color in color_stats.keys()):
                            color_info.append((color, type_name, 'type'))

            # 如果没有颜色信息，显示提示
            if not color_info:
                self.ax_color_legend.text(0.5, 0.5, '暂无颜色信息',
                                        ha='center', va='center',
                                        fontproperties=self.chinese_font, fontsize=10)
                return

            # 🔧 优化：改进布局算法，分离组状态和实体类别信息
            total_items = len(color_info)

            # 🔧 优化：根据项目数量动态调整列数
            if total_items <= 4:
                cols = 2  # 少量项目使用2列
            elif total_items <= 8:
                cols = 3  # 中等数量使用3列
            else:
                cols = 4  # 大量项目最多4列

            # 🔧 优化：分离不同类型的信息
            status_items = [item for item in color_info if item[2] == 'status']
            type_items = [item for item in color_info if item[2] == 'type']

            # 绘制颜色索引
            current_y = 0.95  # 从顶部开始

            # 🔧 优化：先显示组状态信息（更重要）
            if status_items:
                for i, (color, label, _) in enumerate(status_items):
                    col = i % cols
                    if col == 0 and i > 0:  # 换行
                        current_y -= 0.25

                    # 计算位置
                    x = col / cols + 0.05

                    # 🔧 优化：调整颜色方块大小，适应紧凑布局
                    rect_width = 0.04
                    rect_height = 0.15

                    # 创建颜色方块
                    from matplotlib.patches import Rectangle
                    edge_color = self.color_scheme.get('text', '#000000')
                    rect = Rectangle((x, current_y - rect_height/2), rect_width, rect_height,
                                   facecolor=color, edgecolor=edge_color, linewidth=0.5)
                    self.ax_color_legend.add_patch(rect)

                    # 🔧 优化：简化标签文字
                    short_label = label.replace('个实体', '').replace('个组', '')
                    text_x = x + rect_width + 0.01
                    self.ax_color_legend.text(text_x, current_y, short_label,
                                            ha='left', va='center',
                                            fontproperties=self.chinese_font,
                                            fontsize=7)

                # 为类别信息预留空间
                if status_items:
                    current_y -= 0.3

            # 🔧 优化：然后显示实体类别信息
            if type_items:
                for i, (color, label, _) in enumerate(type_items):
                    col = i % cols
                    if col == 0 and i > 0:  # 换行
                        current_y -= 0.25

                    # 计算位置
                    x = col / cols + 0.05

                    # 绘制颜色方块
                    rect_width = 0.04
                    rect_height = 0.15

                    # 创建颜色方块
                    from matplotlib.patches import Rectangle
                    edge_color = self.color_scheme.get('text', '#000000')
                    rect = Rectangle((x, current_y - rect_height/2), rect_width, rect_height,
                                   facecolor=color, edgecolor=edge_color, linewidth=0.5)
                    self.ax_color_legend.add_patch(rect)

                    # 添加文字标签
                    text_x = x + rect_width + 0.01
                    self.ax_color_legend.text(text_x, current_y, label,
                                            ha='left', va='center',
                                            fontproperties=self.chinese_font,
                                            fontsize=7)

            # 🔧 优化：去除标题，直接显示内容，节省空间
            # 不再显示"颜色索引"标题，让内容占据更多空间

            # 设置坐标范围
            self.ax_color_legend.set_xlim(0, 1)
            self.ax_color_legend.set_ylim(0, 1)

            print(f"  - ✅ 颜色索引图创建完成: {total_items} 个颜色项")

        except Exception as e:
            print(f"  - ❌ 创建颜色索引图失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_entity_group_status(self, entity, all_groups, groups_info):
        """通过组信息获取实体状态（修复版 - 确保正确返回组标签）"""
        try:
            # 查找实体所属的组
            for group_index, group in enumerate(all_groups):
                # 处理不同格式的组（字典格式或列表格式）
                if isinstance(group, dict):
                    entities = group.get('entities', [])
                elif isinstance(group, list):
                    entities = group
                else:
                    continue

                # 使用安全的实体比较方法
                if self._is_entity_in_group_safe(entity, entities):
                    # 🔧 关键修复：优先使用组状态信息
                    if group_index < len(groups_info) and groups_info[group_index]:
                        group_info = groups_info[group_index]
                        status = group_info.get('status', 'unlabeled')
                        label = group_info.get('label')

                        # 🔧 修复：确保返回组的标签，不是实体的标签
                        if status in ['labeled', 'completed']:
                            return ('labeled', label)
                        elif status == 'auto_labeled':
                            return ('auto_labeled', label)
                        elif status == 'labeling':
                            return ('labeling', label)
                        else:
                            # 其他状态也返回组标签
                            return (status, label)

                    # 🔧 修复：如果没有组信息，不要从实体获取标签，返回None
                    return None

            # 实体不在任何组中
            return None

        except Exception as e:
            print(f"  ⚠️ 获取实体组状态失败: {e}")
            return None

    def _is_entity_in_labeled_list(self, entity, labeled_entities):
        """检查实体是否在已标注列表中（使用多种匹配方式）"""
        try:
            # 使用安全的实体比较方法（修复unhashable dict错误）
            return self._is_entity_in_group_safe(entity, labeled_entities)

        except Exception as e:
            print(f"  ⚠️ 检查实体是否在已标注列表失败: {e}")
            return False

    def _get_category_color(self, label, processor=None):
        """获取分类颜色（修复版 - 确保配色方案一致性）"""
        try:
            # 🔧 关键修复：优先使用处理器的配色方案（最新的用户设置）
            if processor and hasattr(processor, 'current_color_scheme') and processor.current_color_scheme:
                color_scheme = processor.current_color_scheme
                color = color_scheme.get(label, color_scheme.get('other', '#808080'))
                return color

            # 🔧 修复：使用自身的配色方案（确保已同步）
            if hasattr(self, 'color_scheme') and self.color_scheme:
                color = self.color_scheme.get(label, self.color_scheme.get('other', '#808080'))
                return color

            # 🔧 修复：使用current_color_scheme属性
            if hasattr(self, 'current_color_scheme') and self.current_color_scheme:
                color = self.current_color_scheme.get(label, self.current_color_scheme.get('other', '#808080'))
                return color

            # 🔧 修复：移除所有硬编码颜色，如果没有配色方案则返回默认灰色
            return '#808080'

        except Exception as e:
            return '#808080'

    def _is_entity_in_group(self, entity, group_entities):
        """检查实体是否在组中"""
        try:
            entity_id = entity.get('id')
            if entity_id is not None:
                for group_entity in group_entities:
                    if group_entity.get('id') == entity_id:
                        return True

            # 如果没有ID，使用其他属性匹配
            return self._entities_match(entity, group_entities)

        except Exception as e:
            return False

    def _entities_match(self, entity1, entity2_or_list):
        """检查两个实体是否匹配"""
        try:
            # 如果第二个参数是列表，检查第一个实体是否在列表中
            if isinstance(entity2_or_list, list):
                for entity2 in entity2_or_list:
                    if self._entities_match(entity1, entity2):
                        return True
                return False

            entity2 = entity2_or_list

            # 优先使用ID匹配
            if entity1.get('id') is not None and entity2.get('id') is not None:
                return entity1.get('id') == entity2.get('id')

            # 使用类型和坐标匹配
            if (entity1.get('type') == entity2.get('type') and
                entity1.get('layer') == entity2.get('layer')):

                points1 = entity1.get('points', [])
                points2 = entity2.get('points', [])

                if len(points1) == len(points2) and len(points1) > 0:
                    # 简单的坐标匹配
                    return points1[0] == points2[0] if points1 else False

            return False

        except Exception as e:
            return False












