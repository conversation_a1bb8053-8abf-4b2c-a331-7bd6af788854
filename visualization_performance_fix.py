#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化性能修复方案
针对发现的可视化瓶颈问题提供解决方案
"""

import time
from typing import List, Dict, Any, Optional


class VisualizationPerformanceFix:
    """
    可视化性能修复器

    解决可视化时间过长的问题
    """

    def __init__(self):
        """初始化性能修复器"""
        self.entity_cache = {}
        self.last_entity_hash = None
        self.skip_redundant_updates = True

    def should_skip_update(self, entities: List[Dict[str, Any]]) -> bool:
        """判断是否应该跳过冗余更新"""
        if not self.skip_redundant_updates:
            return False

        # 计算实体列表的简单哈希
        entity_hash = hash(str(len(entities)) + str(id(entities[0]) if entities else ""))

        if entity_hash == self.last_entity_hash:
            print("⚡ 跳过冗余可视化更新")
            return True

        self.last_entity_hash = entity_hash
        return False

    def optimize_draw_entities(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """优化实体绘制"""
        if not entities:
            return True

        # 检查是否跳过冗余更新
        if self.should_skip_update(entities):
            return True

        entity_count = len(entities)
        print(f"⚡ 使用批量绘制模式({entity_count}个实体)")
        return self._batch_draw(visualizer, entities)

    def _batch_draw(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """批量绘制模式"""
        try:
            if hasattr(visualizer, 'ax_detail'):
                ax = visualizer.ax_detail
                ax.clear()
                ax.set_aspect('equal')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 按图层分组绘制
                layer_groups = {}
                for entity in entities:
                    layer = entity.get('layer', 'UNKNOWN')
                    if layer not in layer_groups:
                        layer_groups[layer] = []
                    layer_groups[layer].append(entity)

                # 为每个图层批量绘制
                colors = {'A-WALL': 'blue', 'A-WINDOW': 'red', '0': 'green'}

                for layer, layer_entities in layer_groups.items():
                    color = colors.get(layer, 'gray')

                    # 批量收集该图层的坐标
                    all_x, all_y = [], []
                    for entity in layer_entities:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                x_coords = [points[0][0], points[-1][0]]
                                y_coords = [points[0][1], points[-1][1]]
                                all_x.extend(x_coords + [None])
                                all_y.extend(y_coords + [None])

                    # 一次性绘制该图层
                    if all_x and all_y:
                        ax.plot(all_x, all_y, color=color, linewidth=0.5, alpha=0.7, label=layer)

                ax.legend(fontsize=8)
                ax.set_title(f'批量绘制 ({len(entities)}个实体)', fontsize=10)

            print(f"✅ 批量绘制完成")
            return True

        except Exception as e:
            print(f"❌ 批量绘制失败: {e}")
            return False

    def optimize_visualize_overview(self, visualizer, all_entities: List[Dict[str, Any]],
                                  current_group_entities: Optional[List[Dict[str, Any]]] = None,
                                  labeled_entities: Optional[List[Dict[str, Any]]] = None,
                                  **kwargs) -> bool:
        """优化概览可视化（修复版 - 传递完整参数）"""
        if not all_entities:
            return True

        entity_count = len(all_entities)

        # 🔧 修复：检查是否有足够的信息使用批量概览
        processor = kwargs.get('processor')
        current_group_index = kwargs.get('current_group_index')

        # 优先使用批量概览的条件：
        # 1. 有处理器且有组信息 - 可以显示正确颜色
        # 2. 实体数量很大(>=200) - 性能优化需要
        has_processor_with_groups = processor and hasattr(processor, 'all_groups')
        is_large_dataset = entity_count >= 200

        if has_processor_with_groups or is_large_dataset:
            print(f"⚡ 使用批量概览模式({entity_count}个实体)")
            if not has_processor_with_groups:
                print(f"  ⚠️ 无组信息，可能显示默认颜色")
            return self._batch_overview(visualizer, all_entities, current_group_entities, labeled_entities,
                                      processor, current_group_index)
        else:
            # 如果没有足够的信息且实体数量不大，回退到原始方法
            print(f"🔄 批量概览条件不满足，回退到原始方法({entity_count}个实体)")
            return False

    def _batch_overview(self, visualizer, all_entities: List[Dict[str, Any]],
                      current_group_entities: Optional[List[Dict[str, Any]]] = None,
                      labeled_entities: Optional[List[Dict[str, Any]]] = None,
                      processor=None, current_group_index=None) -> bool:
        """批量概览模式（修复版 - 使用正确的颜色获取逻辑）"""
        try:
            if hasattr(visualizer, 'ax_overview'):
                ax = visualizer.ax_overview
                ax.clear()
                ax.set_aspect('equal')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 使用ID集合进行快速查找
                current_ids = {id(e) for e in current_group_entities} if current_group_entities else set()
                labeled_ids = {id(e) for e in labeled_entities} if labeled_entities else set()

                # 🔧 修复：获取最新的配色方案和组信息
                color_scheme = getattr(visualizer, 'color_scheme', {})
                all_groups = getattr(processor, 'all_groups', []) if processor else []
                groups_info = getattr(processor, 'groups_info', []) if processor else []

                print(f"  批量概览使用最新数据: {len(all_groups)} 个组, {len(groups_info)} 个组信息")

                # 🎨 修复：使用配色方案中的颜色，而不是硬编码
                current_color = color_scheme.get('highlight', '#FF0000') # 使用配色方案中的高亮颜色

                # 🎨 改进：按标签分类收集坐标，支持多种颜色
                coord_groups = {}  # {color: {'x': [], 'y': []}}
                current_x, current_y = [], []

                for entity in all_entities:
                    entity_type = entity.get('type', '')
                    coords = self._get_entity_coords_for_batch(entity)

                    if coords:  # 如果成功获取坐标
                        entity_id = id(entity)
                        if entity_id in current_ids:
                            # 当前组使用高亮颜色
                            current_x.extend(coords['x'] + [None])
                            current_y.extend(coords['y'] + [None])
                        else:
                            # 🔧 修复：使用与正常概览一致的颜色获取逻辑
                            entity_color = self._get_entity_color_for_batch_enhanced(
                                entity, color_scheme, visualizer, labeled_entities,
                                all_groups, groups_info, processor
                            )

                            if entity_color not in coord_groups:
                                coord_groups[entity_color] = {'x': [], 'y': []}

                            coord_groups[entity_color]['x'].extend(coords['x'] + [None])
                            coord_groups[entity_color]['y'].extend(coords['y'] + [None])

                # 🎨 修复：批量绘制使用配色方案颜色
                # 绘制按颜色分组的实体
                for color, coords in coord_groups.items():
                    if coords['x'] and coords['y']:
                        ax.plot(coords['x'], coords['y'], color=color, linewidth=0.5, alpha=0.7)

                # 🔴 修复：绘制当前组（增强高亮显示）
                if current_x and current_y:
                    # 绘制两层：底层粗线和顶层细线，增强高亮效果
                    ax.plot(current_x, current_y, color=current_color, linewidth=3.0, alpha=0.7)  # 底层粗线
                    ax.plot(current_x, current_y, color=current_color, linewidth=1.5, alpha=1.0)  # 顶层细线

                current_count = len(current_group_entities) if current_group_entities else 0
                labeled_count = len(labeled_entities) if labeled_entities else 0
                ax.set_title(f'批量概览 (总:{len(all_entities)}, 当前:{current_count}, 已标注:{labeled_count})', fontsize=10)

            print(f"✅ 批量概览完成")
            return True

        except Exception as e:
            print(f"❌ 批量概览失败: {e}")
            return False

    def _get_entity_color_for_batch_enhanced(self, entity, color_scheme, visualizer,
                                           labeled_entities, all_groups, groups_info, processor):
        """🔧 修复：为批量概览获取实体颜色（优先组信息，回退到实体标签）"""
        try:
            # 🔧 优先级1：如果有组信息，使用组信息
            if all_groups and groups_info:
                entity_id = id(entity)

                # 查找实体所属的组
                for group_index, group in enumerate(all_groups):
                    if group and any(id(e) == entity_id for e in group):
                        if group_index < len(groups_info):
                            entity_group_info = groups_info[group_index]
                            return self._get_color_from_group_info(entity_group_info, color_scheme)
                        break

            # 🔧 优先级2：如果没有组信息，使用实体标签直接获取颜色
            entity_label = entity.get('label')
            if entity_label:
                # 根据实体标签获取对应颜色
                label_color_map = {
                    'wall': color_scheme.get('wall', '#8B4513'),
                    'door_window': color_scheme.get('door_window', '#FFD700'),
                    'furniture': color_scheme.get('furniture', '#4DB6AC'),
                    'column': color_scheme.get('column', '#9C27B0'),
                    'stair': color_scheme.get('stair', '#FF5722'),
                    'other': color_scheme.get('other', '#808080')
                }
                return label_color_map.get(entity_label, color_scheme.get('other', '#808080'))

            # 🔧 优先级3：如果有可视化器的增强方法，尝试使用
            if hasattr(visualizer, '_get_entity_display_info_enhanced'):
                try:
                    color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                        entity,
                        labeled_entities=labeled_entities or [],
                        current_group_entities=[],
                        all_groups=all_groups or [],
                        groups_info=groups_info or [],
                        processor=processor
                    )
                    return color
                except Exception as e:
                    pass  # 静默失败，继续下一个方案

            # 🔧 优先级4：检查实体是否在已标注列表中
            if labeled_entities:
                entity_id = id(entity)
                labeled_ids = {id(e) for e in labeled_entities}
                if entity_id in labeled_ids:
                    # 已标注但没有标签，使用默认已标注颜色
                    return color_scheme.get('labeled', '#00AA00')

            # 🔧 最后的备用方案：使用未标注颜色
            return color_scheme.get('unlabeled', '#C0C0C0')

        except Exception as e:
            print(f"  ⚠️ 批量颜色获取失败: {e}")
            return color_scheme.get('other', '#808080')

    def _get_color_from_group_info(self, group_info, color_scheme):
        """🔧 修复：完全基于组信息获取颜色，不使用实体属性"""
        try:
            if not group_info:
                return color_scheme.get('unlabeled', '#C0C0C0')

            # 获取组状态和标签
            status = group_info.get('status', '').lower().strip()
            label = group_info.get('label', '').strip()

            # 优先级1：当前组
            if group_info.get('is_current_group', False):
                return color_scheme.get('current_group', '#FF0000')

            # 优先级2：已标注的组（根据标签获取颜色）
            if status in ['labeled', 'auto_labeled', 'relabeled'] and label and label != '未标注':
                # 标签到颜色的映射
                label_color_map = {
                    'wall': color_scheme.get('wall', '#8B4513'),
                    'door': color_scheme.get('door_window', '#FFD700'),
                    'window': color_scheme.get('door_window', '#FFD700'),
                    'door_window': color_scheme.get('door_window', '#FFD700'),
                    'furniture': color_scheme.get('furniture', '#4DB6AC'),
                    'column': color_scheme.get('column', '#696969'),
                    'stair': color_scheme.get('stair', '#9370DB'),
                    'elevator': color_scheme.get('elevator', '#FF6347'),
                    'other': color_scheme.get('other', '#808080')
                }
                return label_color_map.get(label, color_scheme.get('other', '#808080'))

            # 优先级3：未标注的组
            if status == 'unlabeled':
                return color_scheme.get('unlabeled', '#C0C0C0')

            # 默认颜色
            return color_scheme.get('other', '#808080')

        except Exception as e:
            print(f"  ⚠️ 从组信息获取颜色失败: {e}")
            return color_scheme.get('other', '#808080')

    def _get_entity_coords_for_batch(self, entity):
        """为批量概览获取实体坐标（支持多种实体类型）"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE' and 'points' in entity:
                points = entity['points']
                if len(points) >= 2:
                    return {
                        'x': [points[0][0], points[-1][0]],
                        'y': [points[0][1], points[-1][1]]
                    }

            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and 'points' in entity:
                points = entity['points']
                if len(points) >= 2:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    return {'x': x_coords, 'y': y_coords}

            elif entity_type == 'CIRCLE' and 'center' in entity and 'radius' in entity:
                center = entity['center']
                radius = entity['radius']
                if len(center) >= 2 and radius > 0:
                    # 用圆的边界点近似表示
                    import math
                    angles = [i * math.pi / 8 for i in range(16)]  # 16个点
                    x_coords = [center[0] + radius * math.cos(a) for a in angles] + [center[0] + radius]
                    y_coords = [center[1] + radius * math.sin(a) for a in angles] + [center[1]]
                    return {'x': x_coords, 'y': y_coords}

            elif entity_type == 'ARC' and 'center' in entity and 'radius' in entity:
                center = entity['center']
                radius = entity['radius']
                start_angle = entity.get('start_angle', 0)
                end_angle = entity.get('end_angle', 2 * 3.14159)

                if len(center) >= 2 and radius > 0:
                    # 用弧的采样点表示
                    import math
                    num_points = max(8, int(abs(end_angle - start_angle) * 8 / (2 * math.pi)))
                    angles = [start_angle + i * (end_angle - start_angle) / (num_points - 1)
                             for i in range(num_points)]
                    x_coords = [center[0] + radius * math.cos(a) for a in angles]
                    y_coords = [center[1] + radius * math.sin(a) for a in angles]
                    return {'x': x_coords, 'y': y_coords}

            elif entity_type == 'INSERT' and 'position' in entity:
                # 插入块用一个小十字表示
                pos = entity['position']
                if len(pos) >= 2:
                    size = 1.0  # 十字大小
                    return {
                        'x': [pos[0] - size, pos[0] + size, pos[0], pos[0], pos[0] + size],
                        'y': [pos[1], pos[1], pos[1], pos[1] - size, pos[1] + size]
                    }

            return None

        except Exception as e:
            return None

    def get_performance_settings(self) -> Dict[str, Any]:
        """获取性能设置"""
        return {
            'skip_redundant_updates': self.skip_redundant_updates,
            'optimization_strategy': 'batch_drawing + id_lookup + redundancy_skip'
        }


def apply_visualization_performance_fix(visualizer):
    """为可视化器应用性能修复"""
    fix = VisualizationPerformanceFix()

    # 保存原始方法
    original_draw_entities = visualizer.draw_entities
    original_visualize_overview = visualizer.visualize_overview

    def optimized_draw_entities(entities):
        """优化的绘制实体方法"""
        start_time = time.time()

        # 尝试使用优化绘制
        if fix.optimize_draw_entities(visualizer, entities):
            print(f"⚡ 优化绘制完成: {time.time() - start_time:.3f}秒")
            return

        # 回退到原始方法
        print(f"🔄 使用原始绘制方法")
        original_draw_entities(entities)

    def optimized_visualize_overview(all_entities, current_group_entities=None,
                                   labeled_entities=None, **kwargs):
        """优化的概览可视化方法"""
        start_time = time.time()

        # 尝试使用优化概览
        if fix.optimize_visualize_overview(visualizer, all_entities,
                                         current_group_entities, labeled_entities, **kwargs):
            print(f"⚡ 优化概览完成: {time.time() - start_time:.3f}秒")
            return

        # 回退到原始方法
        print(f"🔄 使用原始概览方法")
        original_visualize_overview(all_entities, current_group_entities, labeled_entities, **kwargs)

    # 替换方法
    visualizer.draw_entities = optimized_draw_entities
    visualizer.visualize_overview = optimized_visualize_overview

    # 添加性能修复标记
    visualizer._performance_fix_applied = True
    visualizer._performance_fix = fix

    print("⚡ 可视化性能修复已应用")
    print(f"   设置: {fix.get_performance_settings()}")


# 全局修复实例
_visualization_performance_fix = None


def get_visualization_performance_fix():
    """获取可视化性能修复实例"""
    global _visualization_performance_fix
    if _visualization_performance_fix is None:
        _visualization_performance_fix = VisualizationPerformanceFix()
    return _visualization_performance_fix
