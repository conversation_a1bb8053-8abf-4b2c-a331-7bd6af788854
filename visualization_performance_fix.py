#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化性能修复方案
针对发现的可视化瓶颈问题提供解决方案
"""

import time
from typing import List, Dict, Any, Optional


class VisualizationPerformanceFix:
    """
    可视化性能修复器

    解决可视化时间过长的问题
    """

    def __init__(self):
        """初始化性能修复器"""
        self.entity_cache = {}
        self.last_entity_hash = None
        self.skip_redundant_updates = True

    def should_skip_update(self, entities: List[Dict[str, Any]]) -> bool:
        """判断是否应该跳过冗余更新"""
        if not self.skip_redundant_updates:
            return False

        # 计算实体列表的简单哈希
        entity_hash = hash(str(len(entities)) + str(id(entities[0]) if entities else ""))

        if entity_hash == self.last_entity_hash:
            print("⚡ 跳过冗余可视化更新")
            return True

        self.last_entity_hash = entity_hash
        return False

    def optimize_draw_entities(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """优化实体绘制"""
        if not entities:
            return True

        # 检查是否跳过冗余更新
        if self.should_skip_update(entities):
            return True

        entity_count = len(entities)
        print(f"⚡ 使用批量绘制模式({entity_count}个实体)")
        return self._batch_draw(visualizer, entities)

    def _batch_draw(self, visualizer, entities: List[Dict[str, Any]]) -> bool:
        """批量绘制模式"""
        try:
            if hasattr(visualizer, 'ax_detail'):
                ax = visualizer.ax_detail
                ax.clear()
                ax.set_aspect('equal')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 按图层分组绘制
                layer_groups = {}
                for entity in entities:
                    layer = entity.get('layer', 'UNKNOWN')
                    if layer not in layer_groups:
                        layer_groups[layer] = []
                    layer_groups[layer].append(entity)

                # 为每个图层批量绘制
                colors = {'A-WALL': 'blue', 'A-WINDOW': 'red', '0': 'green'}

                for layer, layer_entities in layer_groups.items():
                    color = colors.get(layer, 'gray')

                    # 批量收集该图层的坐标
                    all_x, all_y = [], []
                    for entity in layer_entities:
                        if entity.get('type') == 'LINE' and 'points' in entity:
                            points = entity['points']
                            if len(points) >= 2:
                                x_coords = [points[0][0], points[-1][0]]
                                y_coords = [points[0][1], points[-1][1]]
                                all_x.extend(x_coords + [None])
                                all_y.extend(y_coords + [None])

                    # 一次性绘制该图层
                    if all_x and all_y:
                        ax.plot(all_x, all_y, color=color, linewidth=0.5, alpha=0.7, label=layer)

                ax.legend(fontsize=8)
                ax.set_title(f'批量绘制 ({len(entities)}个实体)', fontsize=10)

            print(f"✅ 批量绘制完成")
            return True

        except Exception as e:
            print(f"❌ 批量绘制失败: {e}")
            return False

    def optimize_visualize_overview(self, visualizer, all_entities: List[Dict[str, Any]],
                                  current_group_entities: Optional[List[Dict[str, Any]]] = None,
                                  labeled_entities: Optional[List[Dict[str, Any]]] = None,
                                  **kwargs) -> bool:
        """优化概览可视化（修复版 - 传递完整参数）"""
        if not all_entities:
            return True

        entity_count = len(all_entities)

        # 🔧 修复：检查是否有足够的信息使用批量概览
        processor = kwargs.get('processor')
        current_group_index = kwargs.get('current_group_index')

        # 优先使用批量概览的条件：
        # 1. 有处理器且有组信息 - 可以显示正确颜色
        # 2. 实体数量很大(>=200) - 性能优化需要
        has_processor_with_groups = processor and hasattr(processor, 'all_groups')
        is_large_dataset = entity_count >= 200

        if has_processor_with_groups or is_large_dataset:
            print(f"⚡ 使用批量概览模式({entity_count}个实体)")
            if not has_processor_with_groups:
                print(f"  ⚠️ 无组信息，可能显示默认颜色")
            return self._batch_overview(visualizer, all_entities, current_group_entities, labeled_entities,
                                      processor, current_group_index)
        else:
            # 如果没有足够的信息且实体数量不大，回退到原始方法
            print(f"🔄 批量概览条件不满足，回退到原始方法({entity_count}个实体)")
            return False

    def _batch_overview(self, visualizer, all_entities: List[Dict[str, Any]],
                      current_group_entities: Optional[List[Dict[str, Any]]] = None,
                      labeled_entities: Optional[List[Dict[str, Any]]] = None,
                      processor=None, current_group_index=None) -> bool:
        """批量概览模式（修复版 - 使用正确的颜色获取逻辑）"""
        try:
            if hasattr(visualizer, 'ax_overview'):
                ax = visualizer.ax_overview
                ax.clear()
                ax.set_aspect('equal')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 使用ID集合进行快速查找
                current_ids = {id(e) for e in current_group_entities} if current_group_entities else set()
                labeled_ids = {id(e) for e in labeled_entities} if labeled_entities else set()

                # 🔧 修复：获取最新的配色方案和组信息
                color_scheme = getattr(visualizer, 'color_scheme', {})
                all_groups = getattr(processor, 'all_groups', []) if processor else []
                groups_info = getattr(processor, 'groups_info', []) if processor else []

                print(f"  🔍 批量概览详细调试信息:")
                print(f"    处理器: {processor is not None}")
                print(f"    组数量: {len(all_groups)}")
                print(f"    组信息数量: {len(groups_info)}")

                # 🔧 新增：详细输出组信息内容
                if all_groups and groups_info:
                    print(f"    组详情:")
                    for i, (group, info) in enumerate(zip(all_groups, groups_info)):
                        group_size = len(group) if isinstance(group, list) else 0
                        print(f"      组{i}: {group_size}个实体, 状态={info.get('status', 'unknown')}, 标签={info.get('label', 'unknown')}")
                else:
                    print(f"    ⚠️ 组信息为空，所有实体将显示为灰色")

                print(f"    配色方案: {list(color_scheme.keys())}")
                print(f"    实体总数: {len(all_entities)}")

                # 🎨 修复：使用配色方案中的颜色，而不是硬编码
                current_color = color_scheme.get('highlight', '#FF0000') # 使用配色方案中的高亮颜色

                # 🎨 改进：按标签分类收集坐标，支持多种颜色
                coord_groups = {}  # {color: {'x': [], 'y': []}}
                current_x, current_y = [], []

                for entity in all_entities:
                    entity_type = entity.get('type', '')
                    coords = self._get_entity_coords_for_batch(entity)

                    if coords:  # 如果成功获取坐标
                        entity_id = id(entity)
                        if entity_id in current_ids:
                            # 当前组使用高亮颜色
                            current_x.extend(coords['x'] + [None])
                            current_y.extend(coords['y'] + [None])
                        else:
                            # 🔧 修复：使用与正常概览一致的颜色获取逻辑
                            entity_color = self._get_entity_color_for_batch_enhanced(
                                entity, color_scheme, visualizer, labeled_entities,
                                all_groups, groups_info, processor
                            )

                            # 🔧 移除重复的调试输出

                            if entity_color not in coord_groups:
                                coord_groups[entity_color] = {'x': [], 'y': []}

                            coord_groups[entity_color]['x'].extend(coords['x'] + [None])
                            coord_groups[entity_color]['y'].extend(coords['y'] + [None])

                # 🎨 修复：批量绘制使用配色方案颜色
                # 绘制按颜色分组的实体
                for color, coords in coord_groups.items():
                    if coords['x'] and coords['y']:
                        ax.plot(coords['x'], coords['y'], color=color, linewidth=0.5, alpha=0.7)

                # 🔴 修复：绘制当前组（增强高亮显示）
                if current_x and current_y:
                    # 绘制两层：底层粗线和顶层细线，增强高亮效果
                    ax.plot(current_x, current_y, color=current_color, linewidth=3.0, alpha=0.7)  # 底层粗线
                    ax.plot(current_x, current_y, color=current_color, linewidth=1.5, alpha=1.0)  # 顶层细线

                current_count = len(current_group_entities) if current_group_entities else 0
                labeled_count = len(labeled_entities) if labeled_entities else 0
                ax.set_title(f'批量概览 (总:{len(all_entities)}, 当前:{current_count}, 已标注:{labeled_count})', fontsize=10)

            print(f"✅ 批量概览完成")
            return True

        except Exception as e:
            print(f"❌ 批量概览失败: {e}")
            return False

    def _get_entity_color_for_batch_enhanced(self, entity, color_scheme, visualizer,
                                           labeled_entities, all_groups, groups_info, processor):
        """🔧 修复：为批量概览获取实体颜色（严格基于组信息，不使用实体标签）"""
        try:


            # 🔧 严格要求：只使用组信息获取颜色
            if all_groups and groups_info:
                # 🔧 修复：使用多种方式匹配实体，提高匹配成功率
                entity_id = id(entity)
                entity_layer = entity.get('layer', '') or entity.get('Layer', '') or 'unknown'
                entity_type = entity.get('type', '') or entity.get('Type', '') or 'unknown'

                # 查找实体所属的组
                for group_index, group in enumerate(all_groups):
                    if group:
                        # 🔧 修复：使用多种匹配方式
                        found_in_group = False

                        # 方式1：ID匹配
                        if any(id(e) == entity_id for e in group):
                            found_in_group = True

                        # 方式2：如果ID匹配失败，尝试属性匹配
                        elif not found_in_group and entity_layer:
                            for e in group:
                                if (e.get('layer', '') == entity_layer and
                                    e.get('type', '') == entity_type):
                                    found_in_group = True
                                    break

                        if found_in_group and group_index < len(groups_info):
                            entity_group_info = groups_info[group_index]
                            print(f"    组{group_index}:", end="")
                            color = self._get_color_from_group_info(entity_group_info, color_scheme)
                            print(f"    实体颜色: {entity_layer} -> {color}")
                            return color

                # 🔧 实体不在任何组中，使用未标注颜色
                return color_scheme.get('unlabeled', '#C0C0C0')
            else:
                # 🔧 没有组信息，所有实体显示为未标注颜色
                return color_scheme.get('unlabeled', '#C0C0C0')

        except Exception as e:
            return color_scheme.get('other', '#808080')

    def _get_color_from_group_info(self, group_info, color_scheme):
        """🔧 修复：与组颜色识别优先级一致的颜色获取"""
        try:
            if not group_info:
                return color_scheme.get('unlabeled', '#C0C0C0')

            # 获取组状态和标签
            status = group_info.get('status', '').lower().strip()
            label = group_info.get('label', '').strip()
            is_current = group_info.get('is_current_group', False)

            # 🔧 优先级1：当前组（最高优先级）
            if is_current:
                result_color = color_scheme.get('current_group', '#FF0000')
                print(f"      ✅ 优先级1-当前组: {result_color}")
                return result_color

            # 🔧 优先级2：标注中状态（正在标注的组）
            if status == 'labeling':
                result_color = color_scheme.get('current_group', '#FF0000')
                print(f"      ✅ 优先级2-标注中: {result_color}")
                return result_color

            # 🔧 优先级3：已标注状态（根据标签获取对应颜色）
            if status in ['labeled', 'auto_labeled', 'relabeled'] and label and label != '未标注':
                # 🔧 与主程序保持一致的标签到颜色映射
                label_color_map = {
                    'wall': color_scheme.get('wall', '#8B4513'),
                    'door': color_scheme.get('door_window', '#FFD700'),
                    'window': color_scheme.get('door_window', '#FFD700'),
                    'door_window': color_scheme.get('door_window', '#FFD700'),
                    'furniture': color_scheme.get('furniture', '#4DB6AC'),
                    'bed': color_scheme.get('bed', '#FF69B4'),
                    'sofa': color_scheme.get('sofa', '#32CD32'),
                    'cabinet': color_scheme.get('cabinet', '#8A2BE2'),
                    'dining_table': color_scheme.get('dining_table', '#FF4500'),
                    'appliance': color_scheme.get('appliance', '#00CED1'),
                    'column': color_scheme.get('column', '#9C27B0'),
                    'stair': color_scheme.get('stair', '#FF5722'),
                    'elevator': color_scheme.get('elevator', '#607D8B'),
                    'railing': color_scheme.get('railing', '#795548'),
                    'other': color_scheme.get('other', '#808080')
                }

                result_color = label_color_map.get(label, color_scheme.get('other', '#808080'))
                print(f"      ✅ 优先级3-{label}: {result_color}")
                return result_color

            # 🔧 优先级4：未标注状态
            if status == 'unlabeled':
                result_color = color_scheme.get('unlabeled', '#D3D3D3')
                print(f"      ✅ 优先级4-未标注: {result_color}")
                return result_color

            # 🔧 优先级5：其他状态（pending等）
            if status == 'pending':
                result_color = color_scheme.get('pending', '#FFA500')
                print(f"      ✅ 优先级5-待处理: {result_color}")
                return result_color

            # 🔧 默认颜色（未知状态）
            result_color = color_scheme.get('other', '#808080')
            return result_color

        except Exception as e:
            return color_scheme.get('other', '#808080')

    def _get_entity_coords_for_batch(self, entity):
        """为批量概览获取实体坐标（支持多种实体类型）"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE' and 'points' in entity:
                points = entity['points']
                if len(points) >= 2:
                    return {
                        'x': [points[0][0], points[-1][0]],
                        'y': [points[0][1], points[-1][1]]
                    }

            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and 'points' in entity:
                points = entity['points']
                if len(points) >= 2:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    return {'x': x_coords, 'y': y_coords}

            elif entity_type == 'CIRCLE' and 'center' in entity and 'radius' in entity:
                center = entity['center']
                radius = entity['radius']
                if len(center) >= 2 and radius > 0:
                    # 用圆的边界点近似表示
                    import math
                    angles = [i * math.pi / 8 for i in range(16)]  # 16个点
                    x_coords = [center[0] + radius * math.cos(a) for a in angles] + [center[0] + radius]
                    y_coords = [center[1] + radius * math.sin(a) for a in angles] + [center[1]]
                    return {'x': x_coords, 'y': y_coords}

            elif entity_type == 'ARC' and 'center' in entity and 'radius' in entity:
                center = entity['center']
                radius = entity['radius']
                start_angle = entity.get('start_angle', 0)
                end_angle = entity.get('end_angle', 2 * 3.14159)

                if len(center) >= 2 and radius > 0:
                    # 用弧的采样点表示
                    import math
                    num_points = max(8, int(abs(end_angle - start_angle) * 8 / (2 * math.pi)))
                    angles = [start_angle + i * (end_angle - start_angle) / (num_points - 1)
                             for i in range(num_points)]
                    x_coords = [center[0] + radius * math.cos(a) for a in angles]
                    y_coords = [center[1] + radius * math.sin(a) for a in angles]
                    return {'x': x_coords, 'y': y_coords}

            elif entity_type == 'INSERT' and 'position' in entity:
                # 插入块用一个小十字表示
                pos = entity['position']
                if len(pos) >= 2:
                    size = 1.0  # 十字大小
                    return {
                        'x': [pos[0] - size, pos[0] + size, pos[0], pos[0], pos[0] + size],
                        'y': [pos[1], pos[1], pos[1], pos[1] - size, pos[1] + size]
                    }

            return None

        except Exception as e:
            return None

    def get_performance_settings(self) -> Dict[str, Any]:
        """获取性能设置"""
        return {
            'skip_redundant_updates': self.skip_redundant_updates,
            'optimization_strategy': 'batch_drawing + id_lookup + redundancy_skip'
        }


def apply_visualization_performance_fix(visualizer):
    """为可视化器应用性能修复"""
    fix = VisualizationPerformanceFix()

    # 保存原始方法
    original_draw_entities = visualizer.draw_entities
    original_visualize_overview = visualizer.visualize_overview

    def optimized_draw_entities(entities):
        """优化的绘制实体方法"""
        start_time = time.time()

        # 尝试使用优化绘制
        if fix.optimize_draw_entities(visualizer, entities):
            print(f"⚡ 优化绘制完成: {time.time() - start_time:.3f}秒")
            return

        # 回退到原始方法
        print(f"🔄 使用原始绘制方法")
        original_draw_entities(entities)

    def optimized_visualize_overview(all_entities, current_group_entities=None,
                                   labeled_entities=None, **kwargs):
        """优化的概览可视化方法"""
        start_time = time.time()

        # 尝试使用优化概览
        if fix.optimize_visualize_overview(visualizer, all_entities,
                                         current_group_entities, labeled_entities, **kwargs):
            print(f"⚡ 优化概览完成: {time.time() - start_time:.3f}秒")
            return

        # 回退到原始方法
        print(f"🔄 使用原始概览方法")
        original_visualize_overview(all_entities, current_group_entities, labeled_entities, **kwargs)

    # 替换方法
    visualizer.draw_entities = optimized_draw_entities
    visualizer.visualize_overview = optimized_visualize_overview

    # 添加性能修复标记
    visualizer._performance_fix_applied = True
    visualizer._performance_fix = fix

    print("⚡ 可视化性能修复已应用")
    print(f"   设置: {fix.get_performance_settings()}")


# 全局修复实例
_visualization_performance_fix = None


def get_visualization_performance_fix():
    """获取可视化性能修复实例"""
    global _visualization_performance_fix
    if _visualization_performance_fix is None:
        _visualization_performance_fix = VisualizationPerformanceFix()
    return _visualization_performance_fix
